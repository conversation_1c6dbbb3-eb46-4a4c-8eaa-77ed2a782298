#include "QT_D.h"
#include "ModernTextEdit.h"
#include "MainThread.h"
#include <QApplication>
#include <QTimer>
#include <QScreen>
#include <QGuiApplication>
#include <QLayout>
#include <QPushButton>

QT_D::QT_D(QWidget *parent)
    : QMainWindow(parent)
    , m_dragging(false)
    , m_logger(Logger::getInstance())
    , m_navigationList(nullptr)
    , m_contentStack(nullptr)
    , m_logsPage(nullptr)
    , m_settingsPage(nullptr)
    , m_debugPage(nullptr)
    , m_aboutPage(nullptr)
{
    ui.setupUi(this);
    initializeWindow();
    initializeLogger();
    initializeNavigation();
    connectSignals();

    // 延迟显示欢迎消息，确保窗口完全加载
    QTimer::singleShot(100, this, &QT_D::showWelcomeMessage);

    // 延迟调用LoadDmSoft，确保窗口和日志系统完全初始化
    QTimer::singleShot(200, this, &QT_D::initializeDmSoft);
}

QT_D::~QT_D()
{
}

void QT_D::initializeWindow()
{
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowSystemMenuHint);
    setAttribute(Qt::WA_TranslucentBackground, true);

    // 应用现代化样式
    QString style =
        "QMainWindow {"
        "    background-color: rgba(30, 30, 30, 255);"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 8px;"
        "}"
        "QWidget#titleBar {"
        "    background-color: #2d2d2d;"
        "    border-top-left-radius: 7px;"
        "    border-top-right-radius: 7px;"
        "    border-bottom: 1px solid #3c3c3c;"
        "}"
        "QLabel#titleIcon {"
        "    background-color: #007acc;"
        "    border-radius: 12px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 12px;"
        "}"
        "QLabel#titleLabel {"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    background-color: transparent;"
        "}"
        "QPushButton {"
        "    background-color: #007acc;"
        "    border: none;"
        "    border-radius: 6px;"
        "    color: white;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 16px;"
        "    min-height: 20px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #404040;"
        "}"
        "QPushButton#minimizeButton, QPushButton#closeButton {"
        "    background-color: transparent;"
        "    border-radius: 15px;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    min-width: 30px;"
        "    max-width: 30px;"
        "    min-height: 30px;"
        "    max-height: 30px;"
        "    padding: 0px;"
        "}"
        "QPushButton#closeButton:hover {"
        "    background-color: #e74c3c;"
        "}"
        "QTextEdit {"
        "    background-color: #252525;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    color: #ffffff;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: #007acc;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-height: 30px;"
        "    margin: 0px 2px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: transparent;"
        "    height: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-width: 30px;"
        "    margin: 2px 0px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:horizontal:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:horizontal {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:horizontal {"
        "    background: none;"
        "}"
        "QWidget#contentWidget {"
        "    background-color: #1e1e1e;"
        "    border-bottom-left-radius: 7px;"
        "    border-bottom-right-radius: 7px;"
        "}";
    setStyleSheet(style);

    // 设置窗口图标（如果有的话）
    // setWindowIcon(QIcon(":/icons/app_icon.png"));

    // 确保窗口居中显示
    QScreen* screen = QGuiApplication::primaryScreen();
    if (screen) {
        QRect screenGeometry = screen->geometry();
        int x = (screenGeometry.width() - width()) / 2;
        int y = (screenGeometry.height() - height()) / 2;
        move(x, y);
    }
}

void QT_D::initializeLogger()
{
    // 创建ModernTextEdit替换原来的logTextEdit
    ModernTextEdit* modernLogEdit = new ModernTextEdit(this);
    modernLogEdit->setObjectName("logTextEdit");

    // 替换UI中的logTextEdit
    QWidget* oldLogEdit = ui.logTextEdit;
    QLayout* layout = oldLogEdit->parentWidget()->layout();
    if (layout) {
        layout->replaceWidget(oldLogEdit, modernLogEdit);
        oldLogEdit->deleteLater();
    }

    // 设置现代化日志控件
    m_logger->setModernLogWidget(modernLogEdit);

    // 配置日志系统
    m_logger->setTimestampEnabled(true);
    m_logger->setModuleEnabled(true);
    m_logger->setMaxLines(500);
}

void QT_D::initializeNavigation()
{
    // 获取内容区域
    QWidget* contentWidget = ui.contentWidget;
    QVBoxLayout* originalLayout = qobject_cast<QVBoxLayout*>(contentWidget->layout());

    if (!originalLayout) return;

    // 创建水平布局替换原来的垂直布局
    QHBoxLayout* mainLayout = new QHBoxLayout();
    mainLayout->setSpacing(0);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 创建导航列表
    m_navigationList = new QListWidget();
    m_navigationList->setFixedWidth(150);
    m_navigationList->addItem("日志");
    m_navigationList->addItem("设置");
    m_navigationList->addItem("调试");
    m_navigationList->addItem("关于");

    // 设置导航列表样式
    QString navStyle =
        "QListWidget {"
        "    background-color: #2d2d2d;"
        "    border: none;"
        "    border-right: 1px solid #3c3c3c;"
        "    outline: none;"
        "    font-size: 14px;"
        "}"
        "QListWidget::item {"
        "    background-color: transparent;"
        "    color: #ffffff;"
        "    padding: 15px 20px;"
        "    border-bottom: 1px solid #3c3c3c;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #007acc;"
        "    color: #ffffff;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: #404040;"
        "}";
    m_navigationList->setStyleSheet(navStyle);

    // 创建内容堆叠窗口
    m_contentStack = new QStackedWidget();

    // 创建日志页面（移动现有的日志控件）
    m_logsPage = new QWidget();
    QVBoxLayout* logsLayout = new QVBoxLayout(m_logsPage);
    logsLayout->setContentsMargins(10, 10, 10, 10);

    // 移动现有的控件到日志页面
    QLayoutItem* item;
    while ((item = originalLayout->takeAt(0)) != nullptr) {
        if (item->widget()) {
            logsLayout->addWidget(item->widget());
        }
        delete item;
    }

    // 创建其他页面
    createOtherPages();

    // 添加页面到堆叠窗口
    m_contentStack->addWidget(m_logsPage);
    m_contentStack->addWidget(m_settingsPage);
    m_contentStack->addWidget(m_debugPage);
    m_contentStack->addWidget(m_aboutPage);

    // 添加到主布局
    mainLayout->addWidget(m_navigationList);
    mainLayout->addWidget(m_contentStack);

    // 替换原来的布局
    delete contentWidget->layout();
    contentWidget->setLayout(mainLayout);

    // 设置默认选中第一项
    m_navigationList->setCurrentRow(0);
}

void QT_D::createOtherPages()
{
    // 创建设置页面
    m_settingsPage = new QWidget();
    QVBoxLayout* settingsLayout = new QVBoxLayout(m_settingsPage);
    settingsLayout->setContentsMargins(20, 20, 20, 20);

    QLabel* settingsTitle = new QLabel("设置");
    settingsTitle->setStyleSheet("QLabel { color: #ffffff; font-size: 24px; font-weight: bold; margin-bottom: 20px; }");
    settingsLayout->addWidget(settingsTitle);

    QLabel* settingsContent = new QLabel("这里将显示应用程序的配置选项。");
    settingsContent->setStyleSheet("QLabel { color: #cccccc; font-size: 14px; }");
    settingsLayout->addWidget(settingsContent);
    settingsLayout->addStretch();

    // 创建调试页面
    m_debugPage = new QWidget();
    QVBoxLayout* debugLayout = new QVBoxLayout(m_debugPage);
    debugLayout->setContentsMargins(20, 20, 20, 20);

    QLabel* debugTitle = new QLabel("调试工具");
    debugTitle->setStyleSheet("QLabel { color: #ffffff; font-size: 24px; font-weight: bold; margin-bottom: 20px; }");
    debugLayout->addWidget(debugTitle);

    QLabel* debugContent = new QLabel("这里将显示调试工具和实用程序。");
    debugContent->setStyleSheet("QLabel { color: #cccccc; font-size: 14px; }");
    debugLayout->addWidget(debugContent);
    debugLayout->addStretch();

    // 创建关于页面
    m_aboutPage = new QWidget();
    QVBoxLayout* aboutLayout = new QVBoxLayout(m_aboutPage);
    aboutLayout->setContentsMargins(20, 20, 20, 20);

    QLabel* aboutTitle = new QLabel("关于软件");
    aboutTitle->setStyleSheet("QLabel { color: #ffffff; font-size: 24px; font-weight: bold; margin-bottom: 20px; }");
    aboutLayout->addWidget(aboutTitle);

    QLabel* aboutContent = new QLabel("QT_D 现代化界面\n版本 1.0\n\n基于Qt5.15开发的现代化应用程序。");
    aboutContent->setStyleSheet("QLabel { color: #cccccc; font-size: 14px; line-height: 1.6; }");
    aboutLayout->addWidget(aboutContent);
    aboutLayout->addStretch();
}

void QT_D::connectSignals()
{
    // 连接窗口控制按钮
    connect(ui.minimizeButton, &QPushButton::clicked, this, &QT_D::onMinimizeClicked);
    connect(ui.closeButton, &QPushButton::clicked, this, &QT_D::onCloseClicked);

    // 连接日志测试按钮
    connect(ui.testInfoButton, &QPushButton::clicked, this, &QT_D::onTestInfoClicked);
    connect(ui.testWarningButton, &QPushButton::clicked, this, &QT_D::onTestWarningClicked);
    connect(ui.testErrorButton, &QPushButton::clicked, this, &QT_D::onTestErrorClicked);
    connect(ui.clearLogButton, &QPushButton::clicked, this, &QT_D::onClearLogClicked);

    // 连接导航列表
    if (m_navigationList && m_contentStack) {
        connect(m_navigationList, &QListWidget::currentRowChanged,
                this, &QT_D::onNavigationItemChanged);
    }
}

void QT_D::showWelcomeMessage()
{
    

}

// 鼠标事件处理 - 窗口拖拽功能
void QT_D::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否点击在标题栏区域
        QWidget* titleBar = ui.titleBar;
        QPoint titleBarPos = titleBar->mapFromGlobal(event->globalPos());

        if (titleBar->rect().contains(titleBarPos)) {
            // 排除按钮区域
            QWidget* minimizeBtn = ui.minimizeButton;
            QWidget* closeBtn = ui.closeButton;

            QPoint minBtnPos = minimizeBtn->mapFromGlobal(event->globalPos());
            QPoint closeBtnPos = closeBtn->mapFromGlobal(event->globalPos());

            if (!minimizeBtn->rect().contains(minBtnPos) &&
                !closeBtn->rect().contains(closeBtnPos)) {
                m_dragging = true;
                m_dragPosition = event->globalPos() - frameGeometry().topLeft();
                event->accept();
                return;
            }
        }
    }
    QMainWindow::mousePressEvent(event);
}

void QT_D::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton && m_dragging) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
        return;
    }
    QMainWindow::mouseMoveEvent(event);
}

void QT_D::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        event->accept();
        return;
    }
    QMainWindow::mouseReleaseEvent(event);
}

void QT_D::closeEvent(QCloseEvent* event)
{
    m_logger->info("Window close event triggered", "System");

    // 清理DmSoft资源
    //CleanupDmSoft();

    // 接受关闭事件
    event->accept();

    // 退出应用程序
    QApplication::quit();
}

// 窗口控制按钮槽函数
void QT_D::onMinimizeClicked()
{
    showMinimized();
    m_logger->debug("Window minimized", "Window");
}

void QT_D::onCloseClicked()
{
    m_logger->info("Closing application...", "System");

    // 清理DmSoft资源
    //CleanupDmSoft();

    QApplication::quit();
}

void QT_D::onNavigationItemChanged(int currentRow)
{
    if (m_contentStack && currentRow >= 0 && currentRow < m_contentStack->count()) {
        m_contentStack->setCurrentIndex(currentRow);

        // 记录页面切换
        QStringList pageNames = {"日志", "设置", "调试", "关于"};
        if (currentRow < pageNames.size()) {
            m_logger->debug(QString("切换到页面: %1").arg(pageNames[currentRow]), "Navigation");
        }
    }
}



// 日志测试按钮槽函数
void QT_D::onTestInfoClicked()
{
    static int infoCount = 1;
    m_logger->info(QString("This is info message #%1").arg(infoCount++), "Test");
}

void QT_D::onTestWarningClicked()
{
    static int warningCount = 1;
    m_logger->warning(QString("This is warning message #%1").arg(warningCount++), "Test");
}

void QT_D::onTestErrorClicked()
{
    static int errorCount = 1;
    m_logger->error(QString("This is error message #%1").arg(errorCount++), "Test");
}

void QT_D::onClearLogClicked()
{
    m_logger->clear();
    m_logger->success("Log cleared", "Logger");
}

void QT_D::initializeDmSoft()
{
    m_logger->info("正在加载 DmSoft...", "DmSoft");

    /*if (LoadDmSoft()) {
        m_logger->success("DmSoft initialization completed successfully", "DmSoft");
    } else {
        m_logger->error("DmSoft initialization failed", "DmSoft");
    }*/
}



