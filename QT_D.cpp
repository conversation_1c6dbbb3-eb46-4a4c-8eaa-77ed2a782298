#include "QT_D.h"
#include "ModernStyle.h"
#include <QApplication>
#include <QTimer>
#include <QDesktopWidget>

QT_D::QT_D(QWidget *parent)
    : QMainWindow(parent)
    , m_dragging(false)
    , m_logger(Logger::getInstance())
{
    ui.setupUi(this);
    initializeWindow();
    initializeLogger();
    connectSignals();

    // 延迟显示欢迎消息，确保窗口完全加载
    QTimer::singleShot(100, this, &QT_D::showWelcomeMessage);
}

QT_D::~QT_D()
{
}

void QT_D::initializeWindow()
{
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowSystemMenuHint);
    setAttribute(Qt::WA_TranslucentBackground, false);

    // 应用现代化样式
    setStyleSheet(ModernStyle::getCompleteStyle());

    // 设置窗口图标（如果有的话）
    // setWindowIcon(QIcon(":/icons/app_icon.png"));

    // 确保窗口居中显示
    move(QApplication::desktop()->screen()->rect().center() - rect().center());
}

void QT_D::initializeLogger()
{
    // 设置日志输出控件
    m_logger->setLogWidget(ui.logTextEdit);

    // 配置日志系统
    m_logger->setTimestampEnabled(true);
    m_logger->setModuleEnabled(true);
    m_logger->setMaxLines(500); // 限制最大日志行数
}

void QT_D::connectSignals()
{
    // 连接窗口控制按钮
    connect(ui.minimizeButton, &QPushButton::clicked, this, &QT_D::onMinimizeClicked);
    connect(ui.closeButton, &QPushButton::clicked, this, &QT_D::onCloseClicked);

    // 连接日志测试按钮
    connect(ui.testInfoButton, &QPushButton::clicked, this, &QT_D::onTestInfoClicked);
    connect(ui.testWarningButton, &QPushButton::clicked, this, &QT_D::onTestWarningClicked);
    connect(ui.testErrorButton, &QPushButton::clicked, this, &QT_D::onTestErrorClicked);
    connect(ui.clearLogButton, &QPushButton::clicked, this, &QT_D::onClearLogClicked);
}

void QT_D::showWelcomeMessage()
{
    m_logger->success("应用程序启动成功", "System");
    m_logger->info("欢迎使用 QT_D 现代化界面", "UI");
    m_logger->info("日志系统已初始化完成", "Logger");
    m_logger->debug("窗口尺寸: " + QString::number(width()) + "x" + QString::number(height()), "Window");
}

// 鼠标事件处理 - 窗口拖拽功能
void QT_D::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否点击在标题栏区域
        QWidget* titleBar = ui.titleBar;
        QPoint titleBarPos = titleBar->mapFromGlobal(event->globalPos());

        if (titleBar->rect().contains(titleBarPos)) {
            // 排除按钮区域
            QWidget* minimizeBtn = ui.minimizeButton;
            QWidget* closeBtn = ui.closeButton;

            QPoint minBtnPos = minimizeBtn->mapFromGlobal(event->globalPos());
            QPoint closeBtnPos = closeBtn->mapFromGlobal(event->globalPos());

            if (!minimizeBtn->rect().contains(minBtnPos) &&
                !closeBtn->rect().contains(closeBtnPos)) {
                m_dragging = true;
                m_dragPosition = event->globalPos() - frameGeometry().topLeft();
                event->accept();
                return;
            }
        }
    }
    QMainWindow::mousePressEvent(event);
}

void QT_D::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton && m_dragging) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
        return;
    }
    QMainWindow::mouseMoveEvent(event);
}

void QT_D::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        event->accept();
        return;
    }
    QMainWindow::mouseReleaseEvent(event);
}

// 窗口控制按钮槽函数
void QT_D::onMinimizeClicked()
{
    showMinimized();
    m_logger->debug("窗口已最小化", "Window");
}

void QT_D::onCloseClicked()
{
    m_logger->info("正在关闭应用程序...", "System");
    QApplication::quit();
}

// 日志测试按钮槽函数
void QT_D::onTestInfoClicked()
{
    static int infoCount = 1;
    m_logger->info(QString("这是第 %1 条信息日志消息").arg(infoCount++), "Test");
}

void QT_D::onTestWarningClicked()
{
    static int warningCount = 1;
    m_logger->warning(QString("这是第 %1 条警告日志消息").arg(warningCount++), "Test");
}

void QT_D::onTestErrorClicked()
{
    static int errorCount = 1;
    m_logger->error(QString("这是第 %1 条错误日志消息").arg(errorCount++), "Test");
}

void QT_D::onClearLogClicked()
{
    m_logger->clear();
    m_logger->success("日志已清空", "Logger");
}

