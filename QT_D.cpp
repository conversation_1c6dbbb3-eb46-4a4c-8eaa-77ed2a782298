#include "QT_D.h"
#include "ModernTextEdit.h"
#include "MainThread.h"
#include <QApplication>
#include <QTimer>
#include <QScreen>
#include <QGuiApplication>
#include <QLayout>
#include <QPushButton>

QT_D::QT_D(QWidget *parent)
    : QMainWindow(parent)
    , m_dragging(false)
    , m_logger(Logger::getInstance())
    , m_navigationPanel(nullptr)
    , m_navigationList(nullptr)
    , m_contentStack(nullptr)
    , m_navigationAnimation(nullptr)
    , m_logsPage(nullptr)
    , m_settingsPage(nullptr)
    , m_debugPage(nullptr)
    , m_aboutPage(nullptr)
    , m_navigationExpanded(false)
    , m_expandedWidth(150)
    , m_collapsedWidth(50)
{
    ui.setupUi(this);
    initializeWindow();
    initializeLogger();
    initializeNavigation();
    connectSignals();

    // 延迟显示欢迎消息，确保窗口完全加载
    QTimer::singleShot(100, this, &QT_D::showWelcomeMessage);

    // 延迟调用LoadDmSoft，确保窗口和日志系统完全初始化
    QTimer::singleShot(200, this, &QT_D::initializeDmSoft);
}

QT_D::~QT_D()
{
}

void QT_D::initializeWindow()
{
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowSystemMenuHint);
    setAttribute(Qt::WA_TranslucentBackground, true);

    // 应用现代化样式
    QString style =
        "QMainWindow {"
        "    background-color: rgba(30, 30, 30, 255);"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 8px;"
        "}"
        "QWidget#titleBar {"
        "    background-color: #2d2d2d;"
        "    border-top-left-radius: 7px;"
        "    border-top-right-radius: 7px;"
        "    border-bottom: 1px solid #3c3c3c;"
        "}"
        "QLabel#titleIcon {"
        "    background-color: #007acc;"
        "    border-radius: 12px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 12px;"
        "}"
        "QLabel#titleLabel {"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    background-color: transparent;"
        "}"
        "QPushButton {"
        "    background-color: #007acc;"
        "    border: none;"
        "    border-radius: 6px;"
        "    color: white;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 16px;"
        "    min-height: 20px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #404040;"
        "}"
        "QPushButton#minimizeButton, QPushButton#closeButton {"
        "    background-color: transparent;"
        "    border-radius: 15px;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    min-width: 30px;"
        "    max-width: 30px;"
        "    min-height: 30px;"
        "    max-height: 30px;"
        "    padding: 0px;"
        "}"
        "QPushButton#closeButton:hover {"
        "    background-color: #e74c3c;"
        "}"
        "QTextEdit {"
        "    background-color: #252525;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    color: #ffffff;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: #007acc;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-height: 30px;"
        "    margin: 0px 2px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: transparent;"
        "    height: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-width: 30px;"
        "    margin: 2px 0px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:horizontal:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:horizontal {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:horizontal {"
        "    background: none;"
        "}"
        "QWidget#contentWidget {"
        "    background-color: #1e1e1e;"
        "    border-bottom-left-radius: 7px;"
        "    border-bottom-right-radius: 7px;"
        "}";
    setStyleSheet(style);

    // 设置窗口图标（如果有的话）
    // setWindowIcon(QIcon(":/icons/app_icon.png"));

    // 确保窗口居中显示
    QScreen* screen = QGuiApplication::primaryScreen();
    if (screen) {
        QRect screenGeometry = screen->geometry();
        int x = (screenGeometry.width() - width()) / 2;
        int y = (screenGeometry.height() - height()) / 2;
        move(x, y);
    }
}

void QT_D::initializeLogger()
{
    // 创建ModernTextEdit替换原来的logTextEdit
    ModernTextEdit* modernLogEdit = new ModernTextEdit(this);
    modernLogEdit->setObjectName("logTextEdit");

    // 替换UI中的logTextEdit
    QWidget* oldLogEdit = ui.logTextEdit;
    QLayout* layout = oldLogEdit->parentWidget()->layout();
    if (layout) {
        layout->replaceWidget(oldLogEdit, modernLogEdit);
        oldLogEdit->deleteLater();
    }

    // 设置现代化日志控件
    m_logger->setModernLogWidget(modernLogEdit);

    // 配置日志系统
    m_logger->setTimestampEnabled(true);
    m_logger->setModuleEnabled(true);
    m_logger->setMaxLines(500);
}

void QT_D::initializeNavigation()
{
    // 获取内容区域
    QWidget* contentWidget = ui.contentWidget;
    QVBoxLayout* originalLayout = qobject_cast<QVBoxLayout*>(contentWidget->layout());

    if (!originalLayout) return;

    // 创建水平布局替换原来的垂直布局
    QHBoxLayout* mainLayout = new QHBoxLayout();
    mainLayout->setSpacing(10);  // 增加间距让两个盒子分离
    mainLayout->setContentsMargins(10, 10, 10, 10);  // 添加外边距

    // 创建导航面板容器
    m_navigationPanel = new QWidget();
    m_navigationPanel->setMinimumWidth(m_collapsedWidth);
    m_navigationPanel->setMaximumWidth(m_collapsedWidth);
    m_navigationPanel->setStyleSheet(
        "QWidget {"
        "    background-color: #2d2d2d;"
        "    border-radius: 6px;"
        "    border: 1px solid #3c3c3c;"
        "}");

    // 安装事件过滤器用于悬停检测
    m_navigationPanel->installEventFilter(this);

    // 创建导航面板的垂直布局
    QVBoxLayout* navPanelLayout = new QVBoxLayout(m_navigationPanel);
    navPanelLayout->setSpacing(0);
    navPanelLayout->setContentsMargins(0, 5, 0, 5);

    // 创建导航列表
    m_navigationList = new QListWidget();
    m_navigationList->addItem("📝 日志");
    m_navigationList->addItem("⚙️ 设置");
    m_navigationList->addItem("🔧 调试");
    m_navigationList->addItem("ℹ️ 关于");

    // 禁用滚动条和调整大小
    m_navigationList->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_navigationList->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_navigationList->setResizeMode(QListView::Fixed);
    m_navigationList->setMovement(QListView::Static);
    m_navigationList->setWordWrap(false);

    // 设置导航列表样式
    QString navStyle =
        "QListWidget {"
        "    background-color: transparent;"
        "    border: none;"
        "    outline: none;"
        "    font-size: 14px;"
        "}"
        "QListWidget::item {"
        "    background-color: transparent;"
        "    color: #ffffff;"
        "    padding: 12px 8px;"
        "    border-bottom: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    margin: 2px 3px;"
        "    text-align: center;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #007acc;"
        "    color: #ffffff;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: #404040;"
        "}";
    m_navigationList->setStyleSheet(navStyle);

    // 添加导航列表到面板
    navPanelLayout->addWidget(m_navigationList);
    navPanelLayout->addStretch();

    // 创建内容堆叠窗口
    m_contentStack = new QStackedWidget();
    m_contentStack->setStyleSheet(
        "QStackedWidget {"
        "    background-color: #1e1e1e;"
        "    border-radius: 6px;"
        "    border: 1px solid #3c3c3c;"
        "}");

    // 创建日志页面（移动现有的日志控件）
    m_logsPage = new QWidget();
    m_logsPage->setStyleSheet(
        "QWidget {"
        "    background-color: transparent;"
        "    border-radius: 8px;"
        "}");
    QVBoxLayout* logsLayout = new QVBoxLayout(m_logsPage);
    logsLayout->setContentsMargins(15, 15, 15, 15);
    logsLayout->setSpacing(10);

    // 移动现有的控件到日志页面
    QLayoutItem* item;
    while ((item = originalLayout->takeAt(0)) != nullptr) {
        if (item->widget()) {
            logsLayout->addWidget(item->widget());
        }
        delete item;
    }

    // 创建其他页面
    createOtherPages();

    // 添加页面到堆叠窗口
    m_contentStack->addWidget(m_logsPage);
    m_contentStack->addWidget(m_settingsPage);
    m_contentStack->addWidget(m_debugPage);
    m_contentStack->addWidget(m_aboutPage);

    // 创建动画 - 同时控制最小宽度和最大宽度
    m_navigationAnimation = new QPropertyAnimation(m_navigationPanel, "minimumWidth", this);
    m_navigationAnimation->setDuration(300);
    m_navigationAnimation->setEasingCurve(QEasingCurve::OutQuart);

    // 连接动画完成信号，同步设置最大宽度
    connect(m_navigationAnimation, &QPropertyAnimation::valueChanged, [this](const QVariant& value) {
        int width = value.toInt();
        m_navigationPanel->setMaximumWidth(width);
        m_navigationPanel->setMinimumWidth(width);
    });

    // 添加到主布局
    mainLayout->addWidget(m_navigationPanel);
    mainLayout->addWidget(m_contentStack);

    // 替换原来的布局
    delete contentWidget->layout();
    contentWidget->setLayout(mainLayout);

    // 设置默认选中第一项
    m_navigationList->setCurrentRow(0);

    // 初始化导航项显示状态
    updateNavigationItems();
}

void QT_D::createOtherPages()
{
    // 创建设置页面
    m_settingsPage = new QWidget();
    m_settingsPage->setStyleSheet("QWidget { background-color: transparent; }");
    QVBoxLayout* settingsLayout = new QVBoxLayout(m_settingsPage);
    settingsLayout->setContentsMargins(25, 25, 25, 25);
    settingsLayout->setSpacing(20);

    QLabel* settingsTitle = new QLabel("⚙️ 设置");
    settingsTitle->setStyleSheet(
        "QLabel {"
        "    color: #ffffff;"
        "    font-size: 28px;"
        "    font-weight: bold;"
        "    padding: 20px;"
        "    background-color: rgba(0, 122, 204, 0.1);"
        "    border-radius: 6px;"
        "    border-left: 4px solid #007acc;"
        "}");
    settingsLayout->addWidget(settingsTitle);

    QLabel* settingsContent = new QLabel("tools");
    settingsContent->setStyleSheet(
        "QLabel {"
        "    color: #cccccc;"
        "    font-size: 14px;"
        "    line-height: 1.6;"
        "    padding: 25px;"
        "    background-color: rgba(255, 255, 255, 0.05);"
        "    border-radius: 6px;"
        "    border: 1px solid rgba(255, 255, 255, 0.1);"
        "}");
    settingsLayout->addWidget(settingsContent);
    settingsLayout->addStretch();

    // 创建调试页面
    m_debugPage = new QWidget();
    m_debugPage->setStyleSheet("QWidget { background-color: transparent; }");
    QVBoxLayout* debugLayout = new QVBoxLayout(m_debugPage);
    debugLayout->setContentsMargins(25, 25, 25, 25);
    debugLayout->setSpacing(20);

    QLabel* debugTitle = new QLabel("🔧 调试工具");
    debugTitle->setStyleSheet(
        "QLabel {"
        "    color: #ffffff;"
        "    font-size: 28px;"
        "    font-weight: bold;"
        "    padding: 20px;"
        "    background-color: rgba(255, 165, 0, 0.1);"
        "    border-radius: 6px;"
        "    border-left: 4px solid #ffa500;"
        "}");
    debugLayout->addWidget(debugTitle);

    QLabel* debugContent = new QLabel("Debug");
    debugContent->setStyleSheet(
        "QLabel {"
        "    color: #cccccc;"
        "    font-size: 14px;"
        "    line-height: 1.6;"
        "    padding: 25px;"
        "    background-color: rgba(255, 255, 255, 0.05);"
        "    border-radius: 6px;"
        "    border: 1px solid rgba(255, 255, 255, 0.1);"
        "}");
    debugLayout->addWidget(debugContent);
    debugLayout->addStretch();

    // 创建关于页面
    m_aboutPage = new QWidget();
    m_aboutPage->setStyleSheet("QWidget { background-color: transparent; }");
    QVBoxLayout* aboutLayout = new QVBoxLayout(m_aboutPage);
    aboutLayout->setContentsMargins(25, 25, 25, 25);
    aboutLayout->setSpacing(20);

    QLabel* aboutTitle = new QLabel("ℹ️ 关于软件");
    aboutTitle->setStyleSheet(
        "QLabel {"
        "    color: #ffffff;"
        "    font-size: 28px;"
        "    font-weight: bold;"
        "    padding: 20px;"
        "    background-color: rgba(0, 200, 100, 0.1);"
        "    border-radius: 6px;"
        "    border-left: 4px solid #00c864;"
        "}");
    aboutLayout->addWidget(aboutTitle);

    QLabel* aboutContent = new QLabel("QT_D 现代化界面\n版本 1.0\n\n基于Qt5.15 + MSVC + C++17开发的现代化应用程序。\n\n特性：\n• 现代化无边框窗口设计\n• 抽屉式侧边导航栏\n• 实时日志系统\n• DmSoft集成支持");
    aboutContent->setStyleSheet(
        "QLabel {"
        "    color: #cccccc;"
        "    font-size: 14px;"
        "    line-height: 1.8;"
        "    padding: 25px;"
        "    background-color: rgba(255, 255, 255, 0.05);"
        "    border-radius: 6px;"
        "    border: 1px solid rgba(255, 255, 255, 0.1);"
        "}");
    aboutLayout->addWidget(aboutContent);
    aboutLayout->addStretch();
}

void QT_D::connectSignals()
{
    // 连接窗口控制按钮
    connect(ui.minimizeButton, &QPushButton::clicked, this, &QT_D::onMinimizeClicked);
    connect(ui.closeButton, &QPushButton::clicked, this, &QT_D::onCloseClicked);

    // 连接日志测试按钮
    connect(ui.testInfoButton, &QPushButton::clicked, this, &QT_D::onTestInfoClicked);
    connect(ui.testWarningButton, &QPushButton::clicked, this, &QT_D::onTestWarningClicked);
    connect(ui.testErrorButton, &QPushButton::clicked, this, &QT_D::onTestErrorClicked);
    connect(ui.clearLogButton, &QPushButton::clicked, this, &QT_D::onClearLogClicked);

    // 连接导航列表
    if (m_navigationList && m_contentStack) {
        connect(m_navigationList, &QListWidget::currentRowChanged,
                this, &QT_D::onNavigationItemChanged);
    }
}

void QT_D::showWelcomeMessage()
{
    

}

// 鼠标事件处理 - 窗口拖拽功能
void QT_D::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否点击在标题栏区域
        QWidget* titleBar = ui.titleBar;
        QPoint titleBarPos = titleBar->mapFromGlobal(event->globalPos());

        if (titleBar->rect().contains(titleBarPos)) {
            // 排除按钮区域
            QWidget* minimizeBtn = ui.minimizeButton;
            QWidget* closeBtn = ui.closeButton;

            QPoint minBtnPos = minimizeBtn->mapFromGlobal(event->globalPos());
            QPoint closeBtnPos = closeBtn->mapFromGlobal(event->globalPos());

            if (!minimizeBtn->rect().contains(minBtnPos) &&
                !closeBtn->rect().contains(closeBtnPos)) {
                m_dragging = true;
                m_dragPosition = event->globalPos() - frameGeometry().topLeft();
                event->accept();
                return;
            }
        }
    }
    QMainWindow::mousePressEvent(event);
}

void QT_D::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton && m_dragging) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
        return;
    }
    QMainWindow::mouseMoveEvent(event);
}

void QT_D::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        event->accept();
        return;
    }
    QMainWindow::mouseReleaseEvent(event);
}

void QT_D::closeEvent(QCloseEvent* event)
{
    m_logger->info("Window close event triggered", "System");

    // 清理DmSoft资源
    //CleanupDmSoft();

    // 接受关闭事件
    event->accept();

    // 退出应用程序
    QApplication::quit();
}

// 窗口控制按钮槽函数
void QT_D::onMinimizeClicked()
{
    showMinimized();
    m_logger->debug("Window minimized", "Window");
}

void QT_D::onCloseClicked()
{
    m_logger->info("Closing application...", "System");

    // 清理DmSoft资源
    //CleanupDmSoft();

    QApplication::quit();
}

bool QT_D::eventFilter(QObject* obj, QEvent* event)
{
    if (obj == m_navigationPanel) {
        if (event->type() == QEvent::Enter) {
            // 鼠标进入导航栏区域，展开
            expandNavigation();
            return true;
        } else if (event->type() == QEvent::Leave) {
            // 鼠标离开导航栏区域，收缩
            collapseNavigation();
            return true;
        }
    }
    return QMainWindow::eventFilter(obj, event);
}

void QT_D::onNavigationItemChanged(int currentRow)
{
    if (m_contentStack && currentRow >= 0 && currentRow < m_contentStack->count()) {
        m_contentStack->setCurrentIndex(currentRow);

        // 记录页面切换
        QStringList pageNames = {"日志", "设置", "调试", "关于"};
        if (currentRow < pageNames.size()) {
            m_logger->debug(QString("切换到页面: %1").arg(pageNames[currentRow]), "Navigation");
        }
    }
}

void QT_D::expandNavigation()
{
    if (m_navigationExpanded || !m_navigationAnimation || !m_navigationPanel) return;

    m_navigationExpanded = true;

    int currentWidth = m_navigationPanel->width();
    int targetWidth = m_expandedWidth;

    // 停止当前动画
    if (m_navigationAnimation->state() == QAbstractAnimation::Running) {
        m_navigationAnimation->stop();
    }

    // 设置动画起始和结束值
    m_navigationAnimation->setStartValue(currentWidth);
    m_navigationAnimation->setEndValue(targetWidth);

    // 立即更新导航项显示
    updateNavigationItems();

    // 启动动画
    m_navigationAnimation->start();

    m_logger->debug(QString("导航栏展开 (%1px -> %2px)").arg(currentWidth).arg(targetWidth), "Navigation");
}

void QT_D::collapseNavigation()
{
    if (!m_navigationExpanded || !m_navigationAnimation || !m_navigationPanel) return;

    m_navigationExpanded = false;

    int currentWidth = m_navigationPanel->width();
    int targetWidth = m_collapsedWidth;

    // 停止当前动画
    if (m_navigationAnimation->state() == QAbstractAnimation::Running) {
        m_navigationAnimation->stop();
    }

    // 设置动画起始和结束值
    m_navigationAnimation->setStartValue(currentWidth);
    m_navigationAnimation->setEndValue(targetWidth);

    // 立即更新导航项显示
    updateNavigationItems();

    // 启动动画
    m_navigationAnimation->start();

    m_logger->debug(QString("导航栏收缩 (%1px -> %2px)").arg(currentWidth).arg(targetWidth), "Navigation");
}

void QT_D::updateNavigationItems()
{
    if (!m_navigationList) return;

    QStringList expandedTexts = {"📝 日志", "⚙️ 设置", "🔧 调试", "ℹ️ 关于"};
    QStringList collapsedTexts = {"📝", "⚙️", "🔧", "ℹ️"};
    QStringList tooltips = {"日志", "设置", "调试", "关于"};

    for (int i = 0; i < m_navigationList->count(); ++i) {
        QListWidgetItem* item = m_navigationList->item(i);
        if (item) {
            if (m_navigationExpanded) {
                item->setText(expandedTexts[i]);
                item->setToolTip("");
                item->setTextAlignment(Qt::AlignLeft | Qt::AlignVCenter);
            } else {
                item->setText(collapsedTexts[i]);
                item->setToolTip(tooltips[i]);
                item->setTextAlignment(Qt::AlignCenter);
            }
        }
    }

    // 强制刷新列表显示
    m_navigationList->update();

    // 更新样式以适应当前状态
    QString navStyle;
    if (m_navigationExpanded) {
        navStyle =
            "QListWidget {"
            "    background-color: transparent;"
            "    border: none;"
            "    outline: none;"
            "    font-size: 14px;"
            "}"
            "QListWidget::item {"
            "    background-color: transparent;"
            "    color: #ffffff;"
            "    padding: 12px 15px;"
            "    border-bottom: 1px solid #3c3c3c;"
            "    border-radius: 6px;"
            "    margin: 2px 5px;"
            "    text-align: left;"
            "}"
            "QListWidget::item:selected {"
            "    background-color: #007acc;"
            "    color: #ffffff;"
            "}"
            "QListWidget::item:hover {"
            "    background-color: #404040;"
            "}";
    } else {
        navStyle =
            "QListWidget {"
            "    background-color: transparent;"
            "    border: none;"
            "    outline: none;"
            "    font-size: 16px;"
            "}"
            "QListWidget::item {"
            "    background-color: transparent;"
            "    color: #ffffff;"
            "    padding: 12px 5px;"
            "    border-bottom: 1px solid #3c3c3c;"
            "    border-radius: 6px;"
            "    margin: 2px 2px;"
            "    text-align: center;"
            "}"
            "QListWidget::item:selected {"
            "    background-color: #007acc;"
            "    color: #ffffff;"
            "}"
            "QListWidget::item:hover {"
            "    background-color: #404040;"
            "}";
    }
    m_navigationList->setStyleSheet(navStyle);
}



// 日志测试按钮槽函数
void QT_D::onTestInfoClicked()
{
    static int infoCount = 1;
    m_logger->info(QString("This is info message #%1").arg(infoCount++), "Test");
}

void QT_D::onTestWarningClicked()
{
    static int warningCount = 1;
    m_logger->warning(QString("This is warning message #%1").arg(warningCount++), "Test");
}

void QT_D::onTestErrorClicked()
{
    static int errorCount = 1;
    m_logger->error(QString("This is error message #%1").arg(errorCount++), "Test");
}

void QT_D::onClearLogClicked()
{
    m_logger->clear();
    m_logger->success("Log cleared", "Logger");
}

void QT_D::initializeDmSoft()
{
    m_logger->info("正在加载 DmSoft...", "DmSoft");

    /*if (LoadDmSoft()) {
        m_logger->success("DmSoft initialization completed successfully", "DmSoft");
    } else {
        m_logger->error("DmSoft initialization failed", "DmSoft");
    }*/
}



