#include "QT_D.h"
#include "ModernTextEdit.h"
#include "MainThread.h"
#include <QApplication>
#include <QTimer>
#include <QScreen>
#include <QGuiApplication>
#include <QLayout>
#include <QPushButton>

QT_D::QT_D(QWidget *parent)
    : QMainWindow(parent)
    , m_dragging(false)
    , m_logger(Logger::getInstance())
    , m_navigationPanel(nullptr)
    , m_navigationList(nullptr)
    , m_contentStack(nullptr)
    , m_navigationAnimation(nullptr)
    , m_logsPage(nullptr)
    , m_settingsPage(nullptr)
    , m_debugPage(nullptr)
    , m_aboutPage(nullptr)
    , m_navigationExpanded(false)
    , m_expandedWidth(150)
    , m_collapsedWidth(50)
    , m_dmSystem(nullptr)
    , m_homeKeyRegistered(false)
{
    ui.setupUi(this);
    initializeWindow();
    initializeLogger();
    initializeNavigation();
    connectSignals();

    // 延迟调用LoadDmSoft，确保窗口和日志系统完全初始化
    QTimer::singleShot(200, this, &QT_D::initializeDmSoft);
}

QT_D::~QT_D()
{
    // 注销热键（如果还未注销）
    if (m_homeKeyRegistered) {
        UnregisterHotKey((HWND)this->winId(), 1);
        m_homeKeyRegistered = false;
    }

    // 清理dmsystem对象
    if (m_dmSystem != nullptr) {
        delete m_dmSystem;
        m_dmSystem = nullptr;
    }
}

void QT_D::initializeWindow()
{
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowSystemMenuHint);
    setAttribute(Qt::WA_TranslucentBackground, true);

    // 应用现代化样式
    QString style =
        "QMainWindow {"
        "    background-color: rgba(30, 30, 30, 255);"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 8px;"
        "}"
        "QWidget#titleBar {"
        "    background-color: #2d2d2d;"
        "    border-top-left-radius: 7px;"
        "    border-top-right-radius: 7px;"
        "    border-bottom: 1px solid #3c3c3c;"
        "}"
        "QLabel#titleIcon {"
        "    background-color: #007acc;"
        "    border-radius: 12px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 12px;"
        "}"
        "QLabel#titleLabel {"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    background-color: transparent;"
        "}"
        "QPushButton {"
        "    background-color: #007acc;"
        "    border: none;"
        "    border-radius: 6px;"
        "    color: white;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 16px;"
        "    min-height: 20px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #404040;"
        "}"
        "QPushButton#minimizeButton, QPushButton#closeButton {"
        "    background-color: transparent;"
        "    border-radius: 15px;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    min-width: 30px;"
        "    max-width: 30px;"
        "    min-height: 30px;"
        "    max-height: 30px;"
        "    padding: 0px;"
        "}"
        "QPushButton#closeButton:hover {"
        "    background-color: #e74c3c;"
        "}"
        "QTextEdit {"
        "    background-color: #252525;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    color: #ffffff;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: #007acc;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-height: 30px;"
        "    margin: 0px 2px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: transparent;"
        "    height: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-width: 30px;"
        "    margin: 2px 0px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:horizontal:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:horizontal {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:horizontal {"
        "    background: none;"
        "}"
        "QWidget#contentWidget {"
        "    background-color: #1e1e1e;"
        "    border-bottom-left-radius: 7px;"
        "    border-bottom-right-radius: 7px;"
        "}";
    setStyleSheet(style);

    // 设置窗口图标（如果有的话）
    // setWindowIcon(QIcon(":/icons/app_icon.png"));

    // 确保窗口居中显示
    QScreen* screen = QGuiApplication::primaryScreen();
    if (screen) {
        QRect screenGeometry = screen->geometry();
        int x = (screenGeometry.width() - width()) / 2;
        int y = (screenGeometry.height() - height()) / 2;
        move(x, y);
    }
}

void QT_D::initializeLogger()
{
    // 创建ModernTextEdit替换原来的logTextEdit
    ModernTextEdit* modernLogEdit = new ModernTextEdit(this);
    modernLogEdit->setObjectName("logTextEdit");

    // 替换UI中的logTextEdit
    QWidget* oldLogEdit = ui.logTextEdit;
    QLayout* layout = oldLogEdit->parentWidget()->layout();
    if (layout) {
        layout->replaceWidget(oldLogEdit, modernLogEdit);
        oldLogEdit->deleteLater();
    }

    // 设置现代化日志控件
    m_logger->setModernLogWidget(modernLogEdit);

    // 配置日志系统
    m_logger->setTimestampEnabled(true);
    m_logger->setModuleEnabled(true);
    m_logger->setMaxLines(500);
}

void QT_D::initializeNavigation()
{
    // 获取内容区域
    QWidget* contentWidget = ui.contentWidget;
    QVBoxLayout* originalLayout = qobject_cast<QVBoxLayout*>(contentWidget->layout());

    if (!originalLayout) return;

    // 创建水平布局替换原来的垂直布局
    QHBoxLayout* mainLayout = new QHBoxLayout();
    mainLayout->setSpacing(10);  // 增加间距让两个盒子分离
    mainLayout->setContentsMargins(10, 10, 10, 10);  // 添加外边距

    // 创建导航面板容器
    m_navigationPanel = new QWidget();
    m_navigationPanel->setMinimumWidth(m_collapsedWidth);
    m_navigationPanel->setMaximumWidth(m_collapsedWidth);
    m_navigationPanel->setStyleSheet(
        "QWidget {"
        "    background-color: #2d2d2d;"
        "    border-radius: 6px;"
        "    border: 1px solid #3c3c3c;"
        "}");

    // 安装事件过滤器用于悬停检测
    m_navigationPanel->installEventFilter(this);

    // 创建导航面板的垂直布局
    QVBoxLayout* navPanelLayout = new QVBoxLayout(m_navigationPanel);
    navPanelLayout->setSpacing(0);
    navPanelLayout->setContentsMargins(0, 10, 0, 10);

    // 创建导航列表
    m_navigationList = new QListWidget();

    // 创建导航项，使用SVG图标
    QStringList itemTexts = {"日志", "设置", "调试", "关于"};
    QStringList svgPaths = {
        ":/QT_D/Ico/24-document.svg",
        ":/QT_D/Ico/24-cogwheel.svg",
        ":/QT_D/Ico/24-bug.svg",
        ":/QT_D/Ico/24-info.svg"
    };
    QStringList iconColors = {"#00d4aa", "#007acc", "#ffa500", "#00c864"};

    for (int i = 0; i < itemTexts.size(); ++i) {
        QListWidgetItem* item = new QListWidgetItem(itemTexts[i]);

        // 创建SVG图标
        QIcon icon = createSvgIcon(svgPaths[i], QColor(iconColors[i]), 24);
        item->setIcon(icon);

        m_navigationList->addItem(item);
    }

    // 禁用滚动条和调整大小
    m_navigationList->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_navigationList->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_navigationList->setResizeMode(QListView::Fixed);
    m_navigationList->setMovement(QListView::Static);
    m_navigationList->setWordWrap(false);

    // 设置固定的项目大小，确保所有项目都能显示
    m_navigationList->setUniformItemSizes(true);

    // 设置图标大小，确保图标居中显示
    m_navigationList->setIconSize(QSize(28, 28));

    // 计算合适的项目高度：确保4个项目能完全显示
    int itemHeight = 45;  // 固定每个项目45px高度，更紧凑
    int itemWidth = m_expandedWidth - 2;  // 稍小于导航栏宽度
    for (int i = 0; i < m_navigationList->count(); ++i) {
        QListWidgetItem* item = m_navigationList->item(i);
        if (item) {
            item->setSizeHint(QSize(itemWidth, itemHeight));
        }
    }

    // 设置导航列表样式
    QString navStyle =
        "QListWidget {"
        "    background-color: transparent;"
        "    border: none;"
        "    outline: none;"
        "    font-size: 14px;"
        "}"
        "QListWidget::item {"
        "    background-color: transparent;"
        "    color: #ffffff;"
        "    padding: 12px 8px;"
        "    border-bottom: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    margin: 2px 3px;"
        "    text-align: center;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #007acc;"
        "    color: #ffffff;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: #404040;"
        "}";
    m_navigationList->setStyleSheet(navStyle);

    // 添加导航列表到面板
    navPanelLayout->addWidget(m_navigationList);
    navPanelLayout->addStretch();

    // 创建内容堆叠窗口
    m_contentStack = new QStackedWidget();
    m_contentStack->setStyleSheet(
        "QStackedWidget {"
        "    background-color: #1e1e1e;"
        "    border-radius: 6px;"
        "    border: 1px solid #3c3c3c;"
        "}");

    // 创建日志页面（移动现有的日志控件）
    m_logsPage = new QWidget();
    m_logsPage->setStyleSheet(
        "QWidget {"
        "    background-color: transparent;"
        "    border-radius: 8px;"
        "}");
    QVBoxLayout* logsLayout = new QVBoxLayout(m_logsPage);
    logsLayout->setContentsMargins(15, 15, 15, 15);
    logsLayout->setSpacing(10);

    // 移动现有的控件到日志页面
    QLayoutItem* item;
    while ((item = originalLayout->takeAt(0)) != nullptr) {
        if (item->widget()) {
            logsLayout->addWidget(item->widget());
        }
        delete item;
    }

    // 创建其他页面
    createOtherPages();

    // 添加页面到堆叠窗口
    m_contentStack->addWidget(m_logsPage);
    m_contentStack->addWidget(m_settingsPage);
    m_contentStack->addWidget(m_debugPage);
    m_contentStack->addWidget(m_aboutPage);

    // 创建动画 - 同时控制最小宽度和最大宽度
    m_navigationAnimation = new QPropertyAnimation(m_navigationPanel, "minimumWidth", this);
    m_navigationAnimation->setDuration(300);
    m_navigationAnimation->setEasingCurve(QEasingCurve::OutQuart);

    // 连接动画完成信号，同步设置最大宽度
    connect(m_navigationAnimation, &QPropertyAnimation::valueChanged, [this](const QVariant& value) {
        int width = value.toInt();
        m_navigationPanel->setMaximumWidth(width);
        m_navigationPanel->setMinimumWidth(width);
    });

    // 添加到主布局
    mainLayout->addWidget(m_navigationPanel);
    mainLayout->addWidget(m_contentStack);

    // 替换原来的布局
    delete contentWidget->layout();
    contentWidget->setLayout(mainLayout);

    // 设置默认选中第一项
    m_navigationList->setCurrentRow(0);

    // 初始化导航项显示状态
    updateNavigationItems();
}

void QT_D::createOtherPages()
{
    // 创建设置页面（深色主题）
    m_settingsPage = new QWidget();
    m_settingsPage->setStyleSheet("QWidget { background-color: transparent; }");

    QVBoxLayout* settingsLayout = new QVBoxLayout(m_settingsPage);
    settingsLayout->setContentsMargins(20, 20, 20, 20);
    settingsLayout->setSpacing(15);

    // 创建WinUI3风格的下拉框区域
    QHBoxLayout* comboBoxLayout = new QHBoxLayout();
    comboBoxLayout->setSpacing(15);

    // 创建四个美化下拉框
    for (int i = 0; i < 4; ++i) {
        QComboBox* winuiCombo = new QComboBox();

        // 设置唯一的对象名称，用于CSS选择器
        winuiCombo->setObjectName(QString("combo_%1").arg(i + 1));

        // 添加示例选项
        winuiCombo->addItem(QString("Option %1-1").arg(i + 1));
        winuiCombo->addItem(QString("Option %1-2").arg(i + 1));
        winuiCombo->addItem(QString("Option %1-3").arg(i + 1));
        winuiCombo->setCurrentIndex(0);

        // 参考CSDN文章的下拉框样式，使用ID选择器
        QString comboId = QString("combo_%1").arg(i + 1);
        winuiCombo->setStyleSheet(
            QString("QComboBox#%1 {"
            "    padding-left: 12px;"
            "    background-color: #4F4F4F;"
            "    border: none;"
            "    border-radius: 11px;"
            "    color: white;"
            "    min-height: 28px;"
            "    font-size: 14px;"
            "    font-family: Microsoft YaHei;"
            "}"
            "QComboBox#%1::drop-down {"
            "    background: transparent;"
            "    subcontrol-origin: padding;"
            "    subcontrol-position: right center;"
            "    width: 24px;"
            "    height: 24px;"
            "    border-radius: 12px;"
            "}"
            "QComboBox#%1::down-arrow {"
            "    border-image: url(:/QT_D/Ico/dropdown.svg);"
            "    width: 18px;"
            "    height: 18px;"
            "}"
            "QComboBox#%1::down-arrow:hover {"
            "    background: qlineargradient(x1:1, y1:1, x2:0, y2:0, stop:0.0 rgba(140, 181, 112, 0), stop:1 rgba(80, 118, 43, 1));"
            "    border-radius: 6px;"
            "}").arg(comboId)
        );

        // 设置下拉列表视图以使样式生效
        winuiCombo->setView(new QListView(this));

        // 去除下拉框的外阴影
        QWidget* containerObj = winuiCombo->view()->parentWidget();
        if (containerObj) {
            containerObj->setWindowFlags(containerObj->windowFlags() | Qt::FramelessWindowHint | Qt::NoDropShadowWindowHint);
            containerObj->setAttribute(Qt::WA_TranslucentBackground);
            containerObj->setStyleSheet("background-color: transparent; border: none; border-radius: 11px;");

            // 设置下拉列表样式
            winuiCombo->view()->setStyleSheet(
                "QListView {"
                "    border: 1px solid rgba(255, 255, 255, 0.25);"
                "    outline: none;"
                "    background: #2D2D2D;"
                "    min-width: 86px;"
                "    border-radius: 11px;"
                "    font-size: 14px;"
                "    color: white;"
                "    padding: 6px;"
                "    margin-top: 3px;"
                "}"
                "QListView::item {"
                "    font-family: Microsoft YaHei;"
                "    font-size: 14px;"
                "    min-width: 86px;"
                "    min-height: 32px;"
                "    border-radius: 11px;"
                "    background: #2D2D2D;"
                "    color: white;"
                "    padding-left: 5px;"
                "}"
                "QListView::item:selected {"
                "    background: qlineargradient(x1:1, y1:1, x2:0, y2:0, stop:0.0 #8CB570, stop:1 #50762B);"
                "}"
                "QScrollBar:vertical {"
                "    border: none;"
                "    background: #272727;"
                "    width: 8px;"
                "    margin: 0px 0px 0px 0px;"
                "    border-radius: 5px;"
                "}"
                "QScrollBar::handle:vertical {"
                "    background: rgba(255, 255, 255, 0.3);"
                "    border-radius: 5px;"
                "    min-height: 20px;"
                "}"
                "QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {"
                "    height: 0px;"
                "    background: none;"
                "}"
                "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {"
                "    background: none;"
                "}"
            );
        }

        comboBoxLayout->addWidget(winuiCombo);
    }

    // 添加弹性空间，让下拉框左对齐
    comboBoxLayout->addStretch();

    settingsLayout->addLayout(comboBoxLayout);

    // 创建表格
    QTableWidget* taskTable = new QTableWidget(0, 6);

    // 设置表头
    QStringList headers = {"ID", "Name", "Mw", "Pl", "Lv", "Status"};
    taskTable->setHorizontalHeaderLabels(headers);

    // 设置深色飞书风格的表格样式
    taskTable->setStyleSheet(
        "QTableWidget {"
        "    background-color: #2d2d2d;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 8px;"
        "    gridline-color: #404040;"
        "    selection-background-color: rgba(0, 122, 204, 0.3);"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    outline: none;"
        "}"
        "QTableWidget::item {"
        "    padding: 12px 16px;"
        "    border: none;"
        "    border-bottom: 1px solid #404040;"
        "    background-color: transparent;"
        "}"
        "QTableWidget::item:selected {"
        "    background-color: rgba(0, 122, 204, 0.3);"
        "    color: #ffffff;"
        "    border: none;"
        "}"
        "QTableWidget::item:hover {"
        "    background-color: #404040;"
        "}"
        "QTableWidget::item:alternate {"
        "    background-color: #353535;"
        "}"
        "QHeaderView {"
        "    background-color: transparent;"
        "}"
        "QHeaderView::section {"
        "    background-color: #404040;"
        "    color: #cccccc;"
        "    padding: 12px 16px;"
        "    border: none;"
        "    border-bottom: 2px solid #3c3c3c;"
        "    border-right: 1px solid #505050;"
        "    font-weight: 600;"
        "    font-size: 13px;"
        "    text-align: left;"
        "}"
        "QHeaderView::section:first {"
        "    border-left: none;"
        "}"
        "QHeaderView::section:last {"
        "    border-right: none;"
        "}"
        "QHeaderView::section:hover {"
        "    background-color: #505050;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background: rgb(95, 95, 95);"
        "    border: 1px solid rgb(60, 60, 60);"
        "    border-radius: 6px;"
        "    min-height: 30px;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background: rgb(127, 127, 127);"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background: rgb(153, 153, 153);"
        "}"
        "QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {"
        "    height: 0px; border: none; background: none;"
        "}"
        "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
    );

    // 设置飞书风格的表格属性
    taskTable->setAlternatingRowColors(true); // 启用交替行颜色
    taskTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    taskTable->setSelectionMode(QAbstractItemView::SingleSelection);
    taskTable->horizontalHeader()->setStretchLastSection(true);
    taskTable->verticalHeader()->setVisible(false);
    taskTable->setShowGrid(false); // 隐藏网格线，使用边框代替
    taskTable->setFocusPolicy(Qt::NoFocus); // 移除焦点框

    // 添加30行示例数据
    for (int i = 0; i < 30; ++i) {
        int row = taskTable->rowCount();
        taskTable->insertRow(row);

        // ID列
        QTableWidgetItem* idItem = new QTableWidgetItem(QString::number(i + 1));
        idItem->setTextAlignment(Qt::AlignCenter);
        taskTable->setItem(row, 0, idItem);

        // Name列
        QTableWidgetItem* nameItem = new QTableWidgetItem("Task " + QString::number(i + 1));
        taskTable->setItem(row, 1, nameItem);

        // Mw列
        QTableWidgetItem* mwItem = new QTableWidgetItem(QString::number((i + 1) * 10));
        mwItem->setTextAlignment(Qt::AlignCenter);
        taskTable->setItem(row, 2, mwItem);

        // Pl列
        QTableWidgetItem* plItem = new QTableWidgetItem(QString::number((i + 1) * 5));
        plItem->setTextAlignment(Qt::AlignCenter);
        taskTable->setItem(row, 3, plItem);

        // Lv列
        QTableWidgetItem* lvItem = new QTableWidgetItem(QString::number(i + 1));
        lvItem->setTextAlignment(Qt::AlignCenter);
        taskTable->setItem(row, 4, lvItem);

        // 状态列 - 使用普通文本
        QTableWidgetItem* statusItem = new QTableWidgetItem("Pending");
        statusItem->setTextAlignment(Qt::AlignCenter);
        taskTable->setItem(row, 5, statusItem);
    }

    // 调整列宽（精确适配内容）
    taskTable->setColumnWidth(0, 56);   // ID列 - 容纳两个数字
    taskTable->setColumnWidth(1, 200);  // Name列 - 保持较宽
    taskTable->setColumnWidth(2, 80);   // Mw列 - 容纳5个数字
    taskTable->setColumnWidth(3, 100);  // Pl列 - 保持原有
    taskTable->setColumnWidth(4, 60);   // Lv列 - 容纳三个数字
    taskTable->setColumnWidth(5, 100);  // Status列 - 适中宽度

    // 调整行高（飞书风格更高的行）
    taskTable->verticalHeader()->setDefaultSectionSize(48);

    // 让表格自适应页面大小
    settingsLayout->addWidget(taskTable, 1); // 设置拉伸因子为1，自适应高度

    // 创建调试页面（空白页面）
    m_debugPage = new QWidget();
    m_debugPage->setStyleSheet("QWidget { background-color: transparent; }");

    // 创建关于页面（空白页面）
    m_aboutPage = new QWidget();
    m_aboutPage->setStyleSheet("QWidget { background-color: transparent; }");
}

void QT_D::connectSignals()
{
    // 连接窗口控制按钮
    connect(ui.minimizeButton, &QPushButton::clicked, this, &QT_D::onMinimizeClicked);
    connect(ui.closeButton, &QPushButton::clicked, this, &QT_D::onCloseClicked);

    // 连接日志控制按钮
    connect(ui.clearLogButton, &QPushButton::clicked, this, &QT_D::onClearLogClicked);

    // 连接导航列表
    if (m_navigationList && m_contentStack) {
        connect(m_navigationList, &QListWidget::currentRowChanged,
                this, &QT_D::onNavigationItemChanged);
    }
}

// 鼠标事件处理 - 窗口拖拽功能
void QT_D::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否点击在标题栏区域
        QWidget* titleBar = ui.titleBar;
        QPoint titleBarPos = titleBar->mapFromGlobal(event->globalPos());

        if (titleBar->rect().contains(titleBarPos)) {
            // 排除按钮区域
            QWidget* minimizeBtn = ui.minimizeButton;
            QWidget* closeBtn = ui.closeButton;

            QPoint minBtnPos = minimizeBtn->mapFromGlobal(event->globalPos());
            QPoint closeBtnPos = closeBtn->mapFromGlobal(event->globalPos());

            if (!minimizeBtn->rect().contains(minBtnPos) &&
                !closeBtn->rect().contains(closeBtnPos)) {
                m_dragging = true;
                m_dragPosition = event->globalPos() - frameGeometry().topLeft();
                event->accept();
                return;
            }
        }
    }
    QMainWindow::mousePressEvent(event);
}

void QT_D::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton && m_dragging) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
        return;
    }
    QMainWindow::mouseMoveEvent(event);
}

void QT_D::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        event->accept();
        return;
    }
    QMainWindow::mouseReleaseEvent(event);
}

void QT_D::closeEvent(QCloseEvent* event)
{
    m_logger->info("Window close event triggered", "System");

    // 清理DmSoft资源
    CleanupDmSoft();

    // 接受关闭事件
    event->accept();

    // 退出应用程序
    QApplication::quit();
}

bool QT_D::nativeEvent(const QByteArray& eventType, void* message, long* result)
{
    if (eventType == "windows_generic_MSG") {
        MSG* msg = static_cast<MSG*>(message);
        if (msg->message == WM_HOTKEY) {
            if (msg->wParam == 1) { // Home键热键ID
                onHomeKeyPressed();
                return true;
            }
        }
    }
    return QMainWindow::nativeEvent(eventType, message, result);
}

// 窗口控制按钮槽函数
void QT_D::onMinimizeClicked()
{
    showMinimized();
    m_logger->debug("Window minimized", "Window");
}

void QT_D::onCloseClicked()
{
    m_logger->info("Closing application...", "System");

    // 清理DmSoft资源
    CleanupDmSoft();

    QApplication::quit();
}

bool QT_D::eventFilter(QObject* obj, QEvent* event)
{
    if (obj == m_navigationPanel) {
        if (event->type() == QEvent::Enter) {
            // 鼠标进入导航栏区域，展开
            expandNavigation();
            return true;
        } else if (event->type() == QEvent::Leave) {
            // 鼠标离开导航栏区域，收缩
            collapseNavigation();
            return true;
        }
    }
    return QMainWindow::eventFilter(obj, event);
}

void QT_D::onNavigationItemChanged(int currentRow)
{
    if (m_contentStack && currentRow >= 0 && currentRow < m_contentStack->count()) {
        m_contentStack->setCurrentIndex(currentRow);

        // 记录页面切换
        QStringList pageNames = {"日志", "设置", "调试", "关于"};
        if (currentRow < pageNames.size()) {
            //m_logger->debug(QString("切换到页面: %1").arg(pageNames[currentRow]), "Navigation");
        }
    }
}

void QT_D::expandNavigation()
{
    if (m_navigationExpanded || !m_navigationAnimation || !m_navigationPanel) return;

    m_navigationExpanded = true;

    int currentWidth = m_navigationPanel->width();
    int targetWidth = m_expandedWidth;

    // 停止当前动画
    if (m_navigationAnimation->state() == QAbstractAnimation::Running) {
        m_navigationAnimation->stop();
    }

    // 设置动画起始和结束值
    m_navigationAnimation->setStartValue(currentWidth);
    m_navigationAnimation->setEndValue(targetWidth);

    // 立即更新导航项显示
    updateNavigationItems();

    // 启动动画
    m_navigationAnimation->start();

    //m_logger->debug(QString("导航栏展开 (%1px -> %2px)").arg(currentWidth).arg(targetWidth), "Navigation");
}

void QT_D::collapseNavigation()
{
    if (!m_navigationExpanded || !m_navigationAnimation || !m_navigationPanel) return;

    m_navigationExpanded = false;

    int currentWidth = m_navigationPanel->width();
    int targetWidth = m_collapsedWidth;

    // 停止当前动画
    if (m_navigationAnimation->state() == QAbstractAnimation::Running) {
        m_navigationAnimation->stop();
    }

    // 设置动画起始和结束值
    m_navigationAnimation->setStartValue(currentWidth);
    m_navigationAnimation->setEndValue(targetWidth);

    // 立即更新导航项显示
    updateNavigationItems();

    // 启动动画
    m_navigationAnimation->start();

    //m_logger->debug(QString("导航栏收缩 (%1px -> %2px)").arg(currentWidth).arg(targetWidth), "Navigation");
}

void QT_D::updateNavigationItems()
{
    if (!m_navigationList) return;

	//QStringList expandedTexts = {"日志", "设置", "调试", "关于"};
	QStringList expandedTexts = { "日志","设置","调试","关于" };
	QStringList collapsedTexts = { "", "", "", "" };  // 收缩时只显示图标
	//QStringList tooltips = {"日志", "设置", "调试", "关于"};
	QStringList tooltips = { "日志","设置","调试","关于" };

    QStringList svgPaths = {
        ":/QT_D/Ico/24-document.svg",
        ":/QT_D/Ico/24-cogwheel.svg",
        ":/QT_D/Ico/24-bug.svg",
        ":/QT_D/Ico/24-info.svg"
    };
    QStringList iconColors = {"#00d4aa", "#007acc", "#ffa500", "#00c864"};

    for (int i = 0; i < m_navigationList->count(); ++i) {
        QListWidgetItem* item = m_navigationList->item(i);
        if (item && i < expandedTexts.size()) {
            if (m_navigationExpanded) {
                item->setText(expandedTexts[i]);
                item->setToolTip("");
                item->setTextAlignment(Qt::AlignCenter);
                // 展开时使用较小的SVG图标
                QIcon icon = createSvgIcon(svgPaths[i], QColor(iconColors[i]), 20);
                item->setIcon(icon);
            } else {
                item->setText(collapsedTexts[i]);
                item->setToolTip(tooltips[i]);
                item->setTextAlignment(Qt::AlignCenter);
                // 收缩时使用较大的SVG图标，但尺寸稍小以便居中
                QIcon icon = createSvgIcon(svgPaths[i], QColor(iconColors[i]), 28);
                item->setIcon(icon);
                // 设置收缩状态的项目尺寸
                item->setSizeHint(QSize(m_collapsedWidth - 2, 45));
            }
        }
    }

    // 更新样式以适应当前状态
    QString navStyle;
    if (m_navigationExpanded) {
        navStyle =
            "QListWidget {"
            "    background-color: transparent;"
            "    border: none;"
            "    outline: none;"
            "    font-size: 14px;"
            "}"
            "QListWidget::item {"
            "    background-color: transparent;"
            "    color: #ffffff;"
            "    padding: 10px 2px;"
            "    border-bottom: 1px solid #3c3c3c;"
            "    border-radius: 6px;"
            "    margin: 1px 1px;"
            "    text-align: center;"
            "}"
            "QListWidget::item:selected {"
            "    background-color: #007acc;"
            "    color: #ffffff;"
            "}"
            "QListWidget::item:hover {"
            "    background-color: #404040;"
            "}";
    } else {
        navStyle =
            "QListWidget {"
            "    background-color: transparent;"
            "    border: none;"
            "    outline: none;"
            "    font-size: 16px;"
            "    qproperty-iconSize: 28px 28px;"
            "}"
            "QListWidget::item {"
            "    background-color: transparent;"
            "    color: #ffffff;"
            "    padding: 8px 0px 8px 6px;"
            "    border-bottom: 1px solid #3c3c3c;"
            "    border-radius: 6px;"
            "    margin: 1px 1px;"
            "    text-align: center;"
            "    qproperty-iconSize: 28px 28px;"
            "}"
            "QListWidget::item:selected {"
            "    background-color: #007acc;"
            "    color: #ffffff;"
            "}"
            "QListWidget::item:hover {"
            "    background-color: #404040;"
            "}"
            "QListWidget::item:selected {"
            "    background-color: #007acc;"
            "    color: #ffffff;"
            "}"
            "QListWidget::item:hover {"
            "    background-color: #404040;"
            "}";
    }
    m_navigationList->setStyleSheet(navStyle);
}



// 日志控制按钮槽函数
void QT_D::onClearLogClicked()
{
    m_logger->clear();
    m_logger->success("Log cleared", "Logger");
}

void QT_D::initializeDmSoft()
{
    m_logger->info("正在加载 DmSoft...", "DmSoft");

    if (LoadDmSoft()) {
        m_logger->success("DmSoft initialization completed successfully", "DmSoft");

        // DmSoft加载成功后注册热键
        registerHotKeys();
    } else {
        m_logger->error("DmSoft initialization failed", "DmSoft");
    }
}

QIcon QT_D::createSvgIcon(const QString& svgPath, const QColor& color, int size)
{
    // 使用QSvgRenderer加载SVG文件
    QSvgRenderer renderer(svgPath);
    if (!renderer.isValid()) {
        m_logger->warning(QString("无法加载SVG图标: %1").arg(svgPath), "Icon");
        // 返回一个简单的文字图标作为后备
        QPixmap pixmap(size, size);
        pixmap.fill(Qt::transparent);
        QPainter painter(&pixmap);
        painter.setRenderHint(QPainter::Antialiasing);
        painter.setPen(color.isValid() ? color : QColor("#ffffff"));
        painter.setFont(QFont("Arial", size/2));
        painter.drawText(pixmap.rect(), Qt::AlignCenter, "?");
        return QIcon(pixmap);
    }

    // 创建指定大小的pixmap
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // 渲染SVG到pixmap
    renderer.render(&painter);

    // 如果指定了颜色，则重新着色
    if (color.isValid()) {
        QPainter colorPainter(&pixmap);
        colorPainter.setCompositionMode(QPainter::CompositionMode_SourceIn);
        colorPainter.fillRect(pixmap.rect(), color);
        colorPainter.end();
    }

    painter.end();
    return QIcon(pixmap);
}

void QT_D::registerHotKeys()
{
    m_logger->info("正在注册热键...", "HotKey");

    // 注册Home键热键 (VK_HOME = 0x24)
    if (RegisterHotKey((HWND)this->winId(), 1, 0, VK_HOME)) {
        m_logger->success("Home key hotkey registered successfully", "HotKey");
        m_homeKeyRegistered = true;
    } else {
        m_logger->error("Home key hotkey registration failed", "HotKey");
        m_homeKeyRegistered = false;
    }
}

void QT_D::onHomeKeyPressed()
{
    m_logger->info("Home key pressed, initializing game system...", "HotKey");
    initializeGameSystem();
}

void QT_D::initializeGameSystem()
{
    try {
        m_logger->info("Initializing game system...", "GameSystem");

        // 查找游戏窗口 - 使用字符数组避免编译问题

        char className[] = {'\xB5', '\xD8', '\xCF', '\xC2', '\xB3', '\xC7', '\xD3', '\xEB', '\xD3', '\xC2', '\xCA', '\xBF', '\0'};
        char windowName[] = {'\xB5', '\xD8', '\xCF', '\xC2', '\xB3', '\xC7', '\xD3', '\xEB', '\xD3', '\xC2', '\xCA', '\xBF', '\xA3', '\xBA', '\xB4', '\xB4', '\xD0', '\xC2', '\xCA', '\xC0', '\xBC', '\xCD', '\0'};
       
        HWND gameWindow = FindWindowA(className, windowName);
        if (gameWindow == NULL) {
            m_logger->error("Game window not found", "GameSystem");
            return;
        }

        // 获取进程ID
        DWORD processId = 0;
        GetWindowThreadProcessId(gameWindow, &processId);
        if (processId == 0) {
            m_logger->error("Failed to get game process ID", "GameSystem");
            return;
        }

        m_logger->info(QString("Found game process ID: %1").arg(processId), "GameSystem");

        // 创建dmsystem对象
        if (m_dmSystem == nullptr) {
            m_dmSystem = new dmsystem();
        }

        // 调用initproess方法
        m_dmSystem->initproess(processId, true);

        // 验证读取功能
        m_logger->info("Verifying memory read functionality...", "GameSystem");
        __int64 testValue = m_dmSystem->ReadDword(0x140000000, 4);

        if (testValue != 0) {
            m_logger->success(QString("Memory read verification successful, value: %1").arg(testValue), "GameSystem");
            m_logger->success("Game system initialization completed", "GameSystem");
        } else {
            m_logger->error("Memory read verification failed, value is 0", "GameSystem");
            m_logger->error("Game system initialization failed", "GameSystem");
            return;
        }

        // 游戏系统初始化完成后释放Home热键
        if (m_homeKeyRegistered) {
            if (UnregisterHotKey((HWND)this->winId(), 1)) {
                m_logger->info("Home key hotkey unregistered successfully", "HotKey");
                m_homeKeyRegistered = false;
            } else {
                m_logger->warning("Failed to unregister Home key hotkey", "HotKey");
            }
        }

    } catch (...) {
        m_logger->error("Exception occurred during game system initialization", "GameSystem");
    }
}





