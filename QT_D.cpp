#include "QT_D.h"
#include "ModernTextEdit.h"
#include "MainThread.h"
#include <QApplication>
#include <QTimer>
#include <QScreen>
#include <QGuiApplication>
#include <QLayout>
#include <QPushButton>

QT_D::QT_D(QWidget *parent)
    : QMainWindow(parent)
    , m_dragging(false)
    , m_logger(Logger::getInstance())
{
    ui.setupUi(this);
    initializeWindow();
    initializeLogger();
    connectSignals();

    // 延迟显示欢迎消息，确保窗口完全加载
    QTimer::singleShot(100, this, &QT_D::showWelcomeMessage);

    // 延迟调用LoadDmSoft，确保窗口和日志系统完全初始化
    QTimer::singleShot(200, this, &QT_D::initializeDmSoft);
}

QT_D::~QT_D()
{
}

void QT_D::initializeWindow()
{
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowSystemMenuHint);
    setAttribute(Qt::WA_TranslucentBackground, true);

    // 应用现代化样式
    QString style =
        "QMainWindow {"
        "    background-color: rgba(30, 30, 30, 255);"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 8px;"
        "}"
        "QWidget#titleBar {"
        "    background-color: #2d2d2d;"
        "    border-top-left-radius: 7px;"
        "    border-top-right-radius: 7px;"
        "    border-bottom: 1px solid #3c3c3c;"
        "}"
        "QLabel#titleIcon {"
        "    background-color: #007acc;"
        "    border-radius: 12px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 12px;"
        "}"
        "QLabel#titleLabel {"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    background-color: transparent;"
        "}"
        "QPushButton {"
        "    background-color: #007acc;"
        "    border: none;"
        "    border-radius: 6px;"
        "    color: white;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 16px;"
        "    min-height: 20px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #404040;"
        "}"
        "QPushButton#minimizeButton, QPushButton#closeButton {"
        "    background-color: transparent;"
        "    border-radius: 15px;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    min-width: 30px;"
        "    max-width: 30px;"
        "    min-height: 30px;"
        "    max-height: 30px;"
        "    padding: 0px;"
        "}"
        "QPushButton#closeButton:hover {"
        "    background-color: #e74c3c;"
        "}"
        "QTextEdit {"
        "    background-color: #252525;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    color: #ffffff;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: #007acc;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-height: 30px;"
        "    margin: 0px 2px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: transparent;"
        "    height: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #5f5f5f;"
        "    border: none;"
        "    border-radius: 6px;"
        "    min-width: 30px;"
        "    margin: 2px 0px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:horizontal:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:horizontal {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:horizontal {"
        "    background: none;"
        "}"
        "QWidget#contentWidget {"
        "    background-color: #1e1e1e;"
        "    border-bottom-left-radius: 7px;"
        "    border-bottom-right-radius: 7px;"
        "}";
    setStyleSheet(style);

    // 设置窗口图标（如果有的话）
    // setWindowIcon(QIcon(":/icons/app_icon.png"));

    // 确保窗口居中显示
    QScreen* screen = QGuiApplication::primaryScreen();
    if (screen) {
        QRect screenGeometry = screen->geometry();
        int x = (screenGeometry.width() - width()) / 2;
        int y = (screenGeometry.height() - height()) / 2;
        move(x, y);
    }
}

void QT_D::initializeLogger()
{
    // 创建ModernTextEdit替换原来的logTextEdit
    ModernTextEdit* modernLogEdit = new ModernTextEdit(this);
    modernLogEdit->setObjectName("logTextEdit");

    // 替换UI中的logTextEdit
    QWidget* oldLogEdit = ui.logTextEdit;
    QLayout* layout = oldLogEdit->parentWidget()->layout();
    if (layout) {
        layout->replaceWidget(oldLogEdit, modernLogEdit);
        oldLogEdit->deleteLater();
    }

    // 设置现代化日志控件
    m_logger->setModernLogWidget(modernLogEdit);

    // 配置日志系统
    m_logger->setTimestampEnabled(true);
    m_logger->setModuleEnabled(true);
    m_logger->setMaxLines(500);
}



void QT_D::connectSignals()
{
    // 连接窗口控制按钮
    connect(ui.minimizeButton, &QPushButton::clicked, this, &QT_D::onMinimizeClicked);
    connect(ui.closeButton, &QPushButton::clicked, this, &QT_D::onCloseClicked);

    // 连接日志测试按钮
    connect(ui.testInfoButton, &QPushButton::clicked, this, &QT_D::onTestInfoClicked);
    connect(ui.testWarningButton, &QPushButton::clicked, this, &QT_D::onTestWarningClicked);
    connect(ui.testErrorButton, &QPushButton::clicked, this, &QT_D::onTestErrorClicked);
    connect(ui.clearLogButton, &QPushButton::clicked, this, &QT_D::onClearLogClicked);
}

void QT_D::showWelcomeMessage()
{
    

}

// 鼠标事件处理 - 窗口拖拽功能
void QT_D::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否点击在标题栏区域
        QWidget* titleBar = ui.titleBar;
        QPoint titleBarPos = titleBar->mapFromGlobal(event->globalPos());

        if (titleBar->rect().contains(titleBarPos)) {
            // 排除按钮区域
            QWidget* minimizeBtn = ui.minimizeButton;
            QWidget* closeBtn = ui.closeButton;

            QPoint minBtnPos = minimizeBtn->mapFromGlobal(event->globalPos());
            QPoint closeBtnPos = closeBtn->mapFromGlobal(event->globalPos());

            if (!minimizeBtn->rect().contains(minBtnPos) &&
                !closeBtn->rect().contains(closeBtnPos)) {
                m_dragging = true;
                m_dragPosition = event->globalPos() - frameGeometry().topLeft();
                event->accept();
                return;
            }
        }
    }
    QMainWindow::mousePressEvent(event);
}

void QT_D::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton && m_dragging) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
        return;
    }
    QMainWindow::mouseMoveEvent(event);
}

void QT_D::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        event->accept();
        return;
    }
    QMainWindow::mouseReleaseEvent(event);
}

void QT_D::closeEvent(QCloseEvent* event)
{
    m_logger->info("Window close event triggered", "System");

    // 清理DmSoft资源
    //CleanupDmSoft();

    // 接受关闭事件
    event->accept();

    // 退出应用程序
    QApplication::quit();
}

// 窗口控制按钮槽函数
void QT_D::onMinimizeClicked()
{
    showMinimized();
    m_logger->debug("Window minimized", "Window");
}

void QT_D::onCloseClicked()
{
    m_logger->info("Closing application...", "System");

    // 清理DmSoft资源
    //CleanupDmSoft();

    QApplication::quit();
}



// 日志测试按钮槽函数
void QT_D::onTestInfoClicked()
{
    static int infoCount = 1;
    m_logger->info(QString("This is info message #%1").arg(infoCount++), "Test");
}

void QT_D::onTestWarningClicked()
{
    static int warningCount = 1;
    m_logger->warning(QString("This is warning message #%1").arg(warningCount++), "Test");
}

void QT_D::onTestErrorClicked()
{
    static int errorCount = 1;
    m_logger->error(QString("This is error message #%1").arg(errorCount++), "Test");
}

void QT_D::onClearLogClicked()
{
    m_logger->clear();
    m_logger->success("Log cleared", "Logger");
}

void QT_D::initializeDmSoft()
{
    m_logger->info("正在加载 DmSoft...", "DmSoft");

    /*if (LoadDmSoft()) {
        m_logger->success("DmSoft initialization completed successfully", "DmSoft");
    } else {
        m_logger->error("DmSoft initialization failed", "DmSoft");
    }*/
}



