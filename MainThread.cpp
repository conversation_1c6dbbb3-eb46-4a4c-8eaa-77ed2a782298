#include "pch.h"
#include "MainThread.h"
#include "MemoryModule.h"


int m_MapPassNum = 0;
dmsystem* m_pDm;

std::string		g_FilePath;
std::wstring	m_FilePath;
std::wstring	g_CoreFile;



#define ProcVer xorstr_(L"S1011 -> 兼容")

//异常捕获函数
long WINAPI ApplicationCrashHandler(EXCEPTION_POINTERS* pException)
{
	EXCEPTION_RECORD* record = pException->ExceptionRecord;

	std::wstring _out(help::dbgprint(xorstr_(
		L"程序遇到异常已经强制结束了!请看以下内容\r"
		"-----异常程序数据采集-----\r"
		"ExceptionAddress: %lx \rExceptionCode:%d\rExceptionFlags:%d\rNumberParameters:%d\r\r"
		"-----------------------------------------\r"
		"<问题自助查询>\r"

		"Q:请管理员运行本软件\r"
		"A:必须以管理员运行本软件,否则会出现此提示\r"
		"-----------------------------------------\r"
		"Q:服务器未响应\r"
		"A:服务器处理的数据过载,可能十分钟恢复,可能需要一段时间,如需要时间较长时则会补偿12小时\r"
		"-----------------------------------------\r"
		"Q:系统为非正式版,暂时不支持非正式版,请下载正式版系统\r"
		"A:只支持正版系统,所有测试版都不支持,包括win10~11中的所有测试版,都不支持,只支持正式发布的版本\r"
		"-----------------------------------------\r"
		"如果没有其中这些问题,说明有其他意外情况,届时请反馈给上级供货商\r\r"),

		(DWORD)record->ExceptionAddress,
		record->ExceptionCode,
		record->ExceptionFlags,
		record->NumberParameters));

	

	return EXCEPTION_EXECUTE_HANDLER;
}
static __declspec(noinline) BOOLEAN CodeCompare(const unsigned char* Code, const unsigned char* Signature, const char* Mask)
{
	for (; *Mask; ++Mask, ++Code, ++Signature)
	{
		if (*Mask == 'x' && *Code != *Signature)
		{
			return FALSE;
		}
	}
	return (*Mask == NULL);
}
static __declspec(noinline) PVOID SearchMemory(PVOID StartAddress, SIZE_T SearchLength, const char* Signature, const char* Mask)
{
	for (size_t i = 0; i < SearchLength; i++)
	{
		if (CodeCompare((const unsigned char*)StartAddress + i, (const unsigned char*)Signature, Mask))
		{
			return (unsigned char*)i;
		}
	}
	return NULL;
}



bool LoadDmSoft()
{
	// "C:\\Users\\<USER>\\Desktop\\云\\版本控制\\S1005\\";
	//std::wstring _out(help::dbgprint(xorstr_(L"LoadDmSoft")));
	TCHAR buffer[MAX_PATH] = { 0 };
	DWORD result = GetCurrentDirectory(MAX_PATH, buffer);
	// 确保路径以反斜杠结尾
	size_t len = _tcslen(buffer);
	if (len > 0 && buffer[len - 1] != '\\') {
		_tcscat_s(buffer, MAX_PATH, _T("\\"));
	}

	// 现在可以安全地拼接文件名
	//_tcscat_s(buffer, MAX_PATH, _T("sys.dll"));


	//_Singleton<_cout>::Ptr()->cout(buffer);

	//g_FilePath = help::wstring2string(help::GetValue(xorstr_(L"installpath")));
	//g_CoreFile = help::GetValue(xorstr_(L"CoreFile"));

	g_FilePath = help::TcharToString(buffer);
	g_CoreFile = buffer;

	//_Singleton<_cout>::Ptr()->cls();
	//_Singleton<_cout>::Ptr()->cout(_Singleton<Error>::Ptr()->getStr(36));
	

#ifdef _DEBUG


#else
	
	
#endif // DEBUG


	auto pDmdll = help::StrReadFile(help::wstring2string(g_CoreFile) + xorstr_("\\sys.dll"));
	auto pLoadD = help::StrReadFile(help::wstring2string(g_CoreFile) + xorstr_("\\Load.dll"));

	//_Singleton<_cout>::Ptr()->cout(string_to_wstring(help::wstring2string(g_CoreFile) + xorstr_("sys.dll")));
	
	// 此处为加载和验证大漠DLL
	if (pDmdll.empty() || pLoadD.empty())
	{
		
		return false;
	}
	if (!LoadDmMemory(pDmdll.c_str(), pDmdll.length(), pLoadD.c_str(), pDmdll.length()))
	{
		return false;
	}
	
	m_pDm = new dmsystem();

	long lreg = m_pDm->Reg(xorstr_("goodtimeplusc1870b29cb1793da03a0d1c078869cb2"), xorstr_("11"));
	if (lreg == 1) 
	{
		int Result;
		// 伪装进程.
		m_pDm->DmGuard(1, xorstr_("f1"));
		// 隐藏模块
		//m_pDm->DmGuard(1, xorstr_("hm DCSocket.dll 0"));

		m_pDm->DmGuard(1, xorstr_("hm 0 0"));

		Result = m_pDm->DmGuard(1, xorstr_("pg"));
		if (Result != 1) {
			//out = xorstr_(L"ERROR_A1:") + DmGuardErrortoString(Result);
			return false;
		}
	
		//// 保护文件
		//m_pDm->DmGuard(1, std::string(xorstr_("h3 p <") + g_FilePath + xorstr_(">")).c_str());

		//m_pDm->DmGuard(1, xorstr_("h3 0"));
		//m_pDm->DmGuard(1, xorstr_("h3 1"));
		//// 防止游戏读取
		m_pDm->DmGuard(1, xorstr_("h3 b <dnf.exe>"));
		// 防止SGUARD64读取
		m_pDm->DmGuard(1, xorstr_("h3 b <SGuard64.exe>"));
		// 防止SGUARD64从NTOSKRNL中读取..
		m_pDm->DmGuard(1, xorstr_("h3 b <ntoskrnl.exe>"));
		// 保护一下句柄
		m_pDm->DmGuard(1, xorstr_("hl"));
		// 隐藏一下驱动
		Result = m_pDm->DmGuard(1, xorstr_("dhide"));
		if (Result != 1)
		{
			return false;
		}
		m_pDm->DmGuard(1, xorstr_("phide remove <0>"));

		Result = m_pDm->DmGuard(1, xorstr_("phide add <0 1 1 1 1 1 1 1 1 1 1 1 1>"));

		if (Result != 1)
		{
			return false;
		}
		Result = m_pDm->SetSimMode(1);

		return true;
	}


	return true;
}

inline std::wstring DmGuardErrortoString(int code)
{
	std::wstring result = xorstr_(L"未知:") + std::to_wstring(code);
	if (code == 0)  { result = xorstr_(L"(-1)暂不支持"); }
	if (code == -2) { result = xorstr_(L"(-2)请管理员运行本软件");}
	if (code == -3) { result = xorstr_(L"(-3)驱动释放失败了,可能是权限不足,请管理员运行\n"
										 "如果管理员运行不行则请看以下步骤:\n"
										 "<1>如果还提示-3,则请下载KB3033929补丁,或者在辅助网盘下载[一键安装环境]\n"
										 "<2>以上操作依然不行,请百度一下自己电脑主板型号如何关闭安全启动");}

	if (code == -7)  { result = xorstr_(L"(-7)系统为非正式版,暂时不支持非正式版,请下载正式版系统"); }
	if (code == -10) { result = xorstr_(L"(-10)按键设置有误,请联系代理并上报此异常"); }

	return result;
}


inline std::wstring RegCodetoString(int code)
{
	std::wstring result = xorstr_(L"未知:") + std::to_wstring(code);
	if (code == -1) { result = xorstr_(L"(-1)无法连接到网络"); }
	if (code == -2) { result = xorstr_(L"(-2)请管理员运行本软件"); }
	if (code == 0)  { result = xorstr_(L"(-1)未知的错误,请及时上报代理"); }
	if (code == 2)  { result = xorstr_(L"服务器未响应,请等待片刻后再次登录,如依然还是本条错误,请等待通知,维护完毕后会补偿12小时"); }
	if (code == 4)  { result = xorstr_(L"旧版本已停用,请检查版本是否为最新版,如不是最新版则请下载最新版本"); }

	return result;
}

