#include "ThemeManager.h"

ThemeManager* ThemeManager::s_instance = nullptr;

ThemeManager* ThemeManager::instance()
{
    if (!s_instance) {
        s_instance = new ThemeManager();
    }
    return s_instance;
}

ThemeManager::ThemeManager(QObject* parent)
    : QObject(parent)
    , m_currentTheme(Dark)
{
    updateColors();
}

void ThemeManager::setTheme(Theme theme)
{
    if (m_currentTheme != theme) {
        m_currentTheme = theme;
        updateColors();
        emit themeChanged(theme);
    }
}

void ThemeManager::toggleTheme()
{
    setTheme(m_currentTheme == Dark ? Light : Dark);
}

void ThemeManager::updateColors()
{
    if (m_currentTheme == Dark) {
        m_windowBackground = QColor(30, 30, 30);
        m_windowBorder = QColor(60, 60, 60);
        m_titleBarBackground = QColor(45, 45, 45);
        m_buttonBackground = QColor(60, 60, 60);
        m_buttonHover = QColor(80, 80, 80);
        m_buttonPressed = QColor(100, 100, 100);
        m_buttonText = QColor(255, 255, 255);
        m_textColor = QColor(255, 255, 255);
        m_logBackground = QColor(37, 37, 37);
        m_logBorder = QColor(60, 60, 60);
        m_logSelection = QColor(0, 122, 204);
        m_scrollBarHandle = QColor(95, 95, 95);
        m_scrollBarHandleHover = QColor(127, 127, 127);
        m_scrollBarHandlePressed = QColor(153, 153, 153);
    } else {
        m_windowBackground = QColor(240, 240, 240);
        m_windowBorder = QColor(200, 200, 200);
        m_titleBarBackground = QColor(220, 220, 220);
        m_buttonBackground = QColor(230, 230, 230);
        m_buttonHover = QColor(210, 210, 210);
        m_buttonPressed = QColor(190, 190, 190);
        m_buttonText = QColor(50, 50, 50);
        m_textColor = QColor(50, 50, 50);
        m_logBackground = QColor(255, 255, 255);
        m_logBorder = QColor(200, 200, 200);
        m_logSelection = QColor(0, 120, 215);
        m_scrollBarHandle = QColor(160, 160, 160);
        m_scrollBarHandleHover = QColor(140, 140, 140);
        m_scrollBarHandlePressed = QColor(120, 120, 120);
    }
}

QString ThemeManager::getMainWindowStyle() const
{
    return QString(
        "QMainWindow {"
        "    background-color: rgba(%1, %2, %3, 255);"
        "    border: 1px solid rgb(%4, %5, %6);"
        "    border-radius: 8px;"
        "}")
        .arg(m_windowBackground.red())
        .arg(m_windowBackground.green())
        .arg(m_windowBackground.blue())
        .arg(m_windowBorder.red())
        .arg(m_windowBorder.green())
        .arg(m_windowBorder.blue());
}

QString ThemeManager::getTitleBarStyle() const
{
    return QString(
        "QWidget#titleBar {"
        "    background-color: rgb(%1, %2, %3);"
        "    border-top-left-radius: 7px;"
        "    border-top-right-radius: 7px;"
        "    border-bottom: 1px solid rgb(%4, %5, %6);"
        "}")
        .arg(m_titleBarBackground.red())
        .arg(m_titleBarBackground.green())
        .arg(m_titleBarBackground.blue())
        .arg(m_windowBorder.red())
        .arg(m_windowBorder.green())
        .arg(m_windowBorder.blue());
}

QString ThemeManager::getButtonStyle() const
{
    return QString(
        "QPushButton {"
        "    background-color: rgb(%1, %2, %3);"
        "    border: none;"
        "    border-radius: 4px;"
        "    color: rgb(%7, %8, %9);"
        "    font-weight: bold;"
        "    padding: 5px 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgb(%4, %5, %6);"
        "}"
        "QPushButton:pressed {"
        "    background-color: rgb(%10, %11, %12);"
        "}")
        .arg(m_buttonBackground.red())
        .arg(m_buttonBackground.green())
        .arg(m_buttonBackground.blue())
        .arg(m_buttonHover.red())
        .arg(m_buttonHover.green())
        .arg(m_buttonHover.blue())
        .arg(m_buttonText.red())
        .arg(m_buttonText.green())
        .arg(m_buttonText.blue())
        .arg(m_buttonPressed.red())
        .arg(m_buttonPressed.green())
        .arg(m_buttonPressed.blue());
}

QString ThemeManager::getLogTextEditStyle() const
{
    return QString(
        "QTextEdit {"
        "    background-color: rgb(%1, %2, %3);"
        "    border: 1px solid rgb(%4, %5, %6);"
        "    border-radius: 6px;"
        "    color: rgb(%7, %8, %9);"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: rgb(%10, %11, %12);"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background: rgb(%13, %14, %15);"
        "    border: 1px solid rgb(%4, %5, %6);"
        "    border-radius: 6px;"
        "    min-height: 30px;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background: rgb(%16, %17, %18);"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background: rgb(%19, %20, %21);"
        "}"
        "QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {"
        "    height: 0px; border: none; background: none;"
        "}"
        "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: transparent;"
        "    height: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background: rgb(%13, %14, %15);"
        "    border: 1px solid rgb(%4, %5, %6);"
        "    border-radius: 6px;"
        "    min-width: 30px;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background: rgb(%16, %17, %18);"
        "}"
        "QScrollBar::handle:horizontal:pressed {"
        "    background: rgb(%19, %20, %21);"
        "}"
        "QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {"
        "    width: 0px; border: none; background: none;"
        "}"
        "QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {"
        "    background: none;"
        "}")
        .arg(m_logBackground.red()).arg(m_logBackground.green()).arg(m_logBackground.blue())
        .arg(m_logBorder.red()).arg(m_logBorder.green()).arg(m_logBorder.blue())
        .arg(m_textColor.red()).arg(m_textColor.green()).arg(m_textColor.blue())
        .arg(m_logSelection.red()).arg(m_logSelection.green()).arg(m_logSelection.blue())
        .arg(m_scrollBarHandle.red()).arg(m_scrollBarHandle.green()).arg(m_scrollBarHandle.blue())
        .arg(m_scrollBarHandleHover.red()).arg(m_scrollBarHandleHover.green()).arg(m_scrollBarHandleHover.blue())
        .arg(m_scrollBarHandlePressed.red()).arg(m_scrollBarHandlePressed.green()).arg(m_scrollBarHandlePressed.blue());
}