/********************************************************************************
** Form generated from reading UI file 'QT_D.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_QT_D_H
#define UI_QT_D_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCommandLinkButton>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QToolButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_QT_DClass
{
public:
    QWidget *centralWidget;
    QRadioButton *radioButton;
    QToolButton *toolButton;
    QPushButton *pushButton;
    QCommandLinkButton *commandLinkButton;
    QMenuBar *menuBar;
    QToolBar *mainToolBar;
    QStatusBar *statusBar;

    void setupUi(QMainWindow *QT_DClass)
    {
        if (QT_DClass->objectName().isEmpty())
            QT_DClass->setObjectName(QString::fromUtf8("QT_DClass"));
        QT_DClass->resize(600, 400);
        centralWidget = new QWidget(QT_DClass);
        centralWidget->setObjectName(QString::fromUtf8("centralWidget"));
        radioButton = new QRadioButton(centralWidget);
        radioButton->setObjectName(QString::fromUtf8("radioButton"));
        radioButton->setGeometry(QRect(140, 130, 95, 19));
        toolButton = new QToolButton(centralWidget);
        toolButton->setObjectName(QString::fromUtf8("toolButton"));
        toolButton->setGeometry(QRect(200, 80, 131, 31));
        pushButton = new QPushButton(centralWidget);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));
        pushButton->setGeometry(QRect(110, 180, 75, 23));
        commandLinkButton = new QCommandLinkButton(centralWidget);
        commandLinkButton->setObjectName(QString::fromUtf8("commandLinkButton"));
        commandLinkButton->setGeometry(QRect(260, 210, 185, 41));
        QT_DClass->setCentralWidget(centralWidget);
        menuBar = new QMenuBar(QT_DClass);
        menuBar->setObjectName(QString::fromUtf8("menuBar"));
        menuBar->setGeometry(QRect(0, 0, 600, 21));
        QT_DClass->setMenuBar(menuBar);
        mainToolBar = new QToolBar(QT_DClass);
        mainToolBar->setObjectName(QString::fromUtf8("mainToolBar"));
        QT_DClass->addToolBar(Qt::TopToolBarArea, mainToolBar);
        statusBar = new QStatusBar(QT_DClass);
        statusBar->setObjectName(QString::fromUtf8("statusBar"));
        QT_DClass->setStatusBar(statusBar);

        retranslateUi(QT_DClass);

        QMetaObject::connectSlotsByName(QT_DClass);
    } // setupUi

    void retranslateUi(QMainWindow *QT_DClass)
    {
        QT_DClass->setWindowTitle(QCoreApplication::translate("QT_DClass", "QT_D", nullptr));
        radioButton->setText(QCoreApplication::translate("QT_DClass", "RadioButton", nullptr));
        toolButton->setText(QCoreApplication::translate("QT_DClass", "...", nullptr));
        pushButton->setText(QCoreApplication::translate("QT_DClass", "PushButton", nullptr));
        commandLinkButton->setText(QCoreApplication::translate("QT_DClass", "CommandLinkButton", nullptr));
    } // retranslateUi

};

namespace Ui {
    class QT_DClass: public Ui_QT_DClass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_QT_D_H
