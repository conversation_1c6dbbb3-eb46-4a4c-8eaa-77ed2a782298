/********************************************************************************
** Form generated from reading UI file 'QT_D.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_QT_D_H
#define UI_QT_D_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_QT_DClass
{
public:
    QWidget *centralWidget;
    QVBoxLayout *mainLayout;
    QWidget *titleBar;
    QHBoxLayout *titleBarLayout;
    QLabel *titleIcon;
    QLabel *titleLabel;
    QSpacerItem *titleSpacer;
    QPushButton *minimizeButton;
    QPushButton *closeButton;
    QWidget *contentWidget;
    QVBoxLayout *contentLayout;
    QTextEdit *logTextEdit;
    QWidget *controlsWidget;
    QHBoxLayout *controlsLayout;
    QPushButton *testInfoButton;
    QPushButton *testWarningButton;
    QPushButton *testErrorButton;
    QSpacerItem *controlsSpacer;
    QPushButton *clearLogButton;

    void setupUi(QMainWindow *QT_DClass)
    {
        if (QT_DClass->objectName().isEmpty())
            QT_DClass->setObjectName(QString::fromUtf8("QT_DClass"));
        QT_DClass->resize(900, 600);
        QT_DClass->setMinimumSize(QSize(800, 500));
        QT_DClass->setWindowFlags(Qt::FramelessWindowHint);
        QT_DClass->setStyleSheet(QString::fromUtf8("\n"
"    QMainWindow {\n"
"        background-color: #1e1e1e;\n"
"        border: 1px solid #3c3c3c;\n"
"        border-radius: 8px;\n"
"    }\n"
"   "));
        centralWidget = new QWidget(QT_DClass);
        centralWidget->setObjectName(QString::fromUtf8("centralWidget"));
        centralWidget->setStyleSheet(QString::fromUtf8("\n"
"     QWidget {\n"
"         background-color: transparent;\n"
"     }\n"
"    "));
        mainLayout = new QVBoxLayout(centralWidget);
        mainLayout->setSpacing(0);
        mainLayout->setContentsMargins(11, 11, 11, 11);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        mainLayout->setContentsMargins(1, 1, 1, 1);
        titleBar = new QWidget(centralWidget);
        titleBar->setObjectName(QString::fromUtf8("titleBar"));
        titleBar->setMinimumSize(QSize(0, 40));
        titleBar->setMaximumSize(QSize(16777215, 40));
        titleBar->setStyleSheet(QString::fromUtf8("\n"
"        QWidget#titleBar {\n"
"            background-color: #2d2d2d;\n"
"            border-top-left-radius: 7px;\n"
"            border-top-right-radius: 7px;\n"
"            border-bottom: 1px solid #3c3c3c;\n"
"        }\n"
"       "));
        titleBarLayout = new QHBoxLayout(titleBar);
        titleBarLayout->setSpacing(10);
        titleBarLayout->setContentsMargins(11, 11, 11, 11);
        titleBarLayout->setObjectName(QString::fromUtf8("titleBarLayout"));
        titleBarLayout->setContentsMargins(15, 0, 5, 0);
        titleIcon = new QLabel(titleBar);
        titleIcon->setObjectName(QString::fromUtf8("titleIcon"));
        titleIcon->setMinimumSize(QSize(24, 24));
        titleIcon->setMaximumSize(QSize(24, 24));
        titleIcon->setStyleSheet(QString::fromUtf8("\n"
"           QLabel {\n"
"               background-color: #007acc;\n"
"               border-radius: 12px;\n"
"               color: white;\n"
"               font-weight: bold;\n"
"               font-size: 12px;\n"
"           }\n"
"          "));
        titleIcon->setAlignment(Qt::AlignCenter);

        titleBarLayout->addWidget(titleIcon);

        titleLabel = new QLabel(titleBar);
        titleLabel->setObjectName(QString::fromUtf8("titleLabel"));
        titleLabel->setStyleSheet(QString::fromUtf8("\n"
"           QLabel {\n"
"               color: #ffffff;\n"
"               font-size: 14px;\n"
"               font-weight: 500;\n"
"               background-color: transparent;\n"
"           }\n"
"          "));

        titleBarLayout->addWidget(titleLabel);

        titleSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        titleBarLayout->addItem(titleSpacer);

        minimizeButton = new QPushButton(titleBar);
        minimizeButton->setObjectName(QString::fromUtf8("minimizeButton"));
        minimizeButton->setMinimumSize(QSize(30, 30));
        minimizeButton->setMaximumSize(QSize(30, 30));
        minimizeButton->setStyleSheet(QString::fromUtf8("\n"
"           QPushButton {\n"
"               background-color: transparent;\n"
"               border: none;\n"
"               border-radius: 15px;\n"
"               color: #ffffff;\n"
"               font-size: 16px;\n"
"               font-weight: bold;\n"
"           }\n"
"           QPushButton:hover {\n"
"               background-color: #404040;\n"
"           }\n"
"           QPushButton:pressed {\n"
"               background-color: #505050;\n"
"           }\n"
"          "));

        titleBarLayout->addWidget(minimizeButton);

        closeButton = new QPushButton(titleBar);
        closeButton->setObjectName(QString::fromUtf8("closeButton"));
        closeButton->setMinimumSize(QSize(30, 30));
        closeButton->setMaximumSize(QSize(30, 30));
        closeButton->setStyleSheet(QString::fromUtf8("\n"
"           QPushButton {\n"
"               background-color: transparent;\n"
"               border: none;\n"
"               border-radius: 15px;\n"
"               color: #ffffff;\n"
"               font-size: 14px;\n"
"               font-weight: bold;\n"
"           }\n"
"           QPushButton:hover {\n"
"               background-color: #e74c3c;\n"
"           }\n"
"           QPushButton:pressed {\n"
"               background-color: #c0392b;\n"
"           }\n"
"          "));

        titleBarLayout->addWidget(closeButton);


        mainLayout->addWidget(titleBar);

        contentWidget = new QWidget(centralWidget);
        contentWidget->setObjectName(QString::fromUtf8("contentWidget"));
        contentWidget->setStyleSheet(QString::fromUtf8("\n"
"        QWidget#contentWidget {\n"
"            background-color: #1e1e1e;\n"
"            border-bottom-left-radius: 7px;\n"
"            border-bottom-right-radius: 7px;\n"
"        }\n"
"       "));
        contentLayout = new QVBoxLayout(contentWidget);
        contentLayout->setSpacing(15);
        contentLayout->setContentsMargins(11, 11, 11, 11);
        contentLayout->setObjectName(QString::fromUtf8("contentLayout"));
        contentLayout->setContentsMargins(20, 20, 20, 20);
        logTextEdit = new QTextEdit(contentWidget);
        logTextEdit->setObjectName(QString::fromUtf8("logTextEdit"));
        logTextEdit->setMinimumSize(QSize(0, 200));
        logTextEdit->setStyleSheet(QString::fromUtf8("\n"
"           QTextEdit {\n"
"               background-color: #252525;\n"
"               border: 1px solid #3c3c3c;\n"
"               border-radius: 6px;\n"
"               color: #ffffff;\n"
"               font-family: 'Consolas', 'Monaco', monospace;\n"
"               font-size: 12px;\n"
"               padding: 10px;\n"
"               selection-background-color: #007acc;\n"
"           }\n"
"           QScrollBar:vertical {\n"
"               background-color: #2d2d2d;\n"
"               width: 12px;\n"
"               border-radius: 6px;\n"
"           }\n"
"           QScrollBar::handle:vertical {\n"
"               background-color: #555555;\n"
"               border-radius: 6px;\n"
"               min-height: 20px;\n"
"           }\n"
"           QScrollBar::handle:vertical:hover {\n"
"               background-color: #666666;\n"
"           }\n"
"           QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {\n"
"               border: none;\n"
"               background: none;\n"
"  "
                        "         }\n"
"          "));
        logTextEdit->setReadOnly(true);

        contentLayout->addWidget(logTextEdit);

        controlsWidget = new QWidget(contentWidget);
        controlsWidget->setObjectName(QString::fromUtf8("controlsWidget"));
        controlsLayout = new QHBoxLayout(controlsWidget);
        controlsLayout->setSpacing(15);
        controlsLayout->setContentsMargins(11, 11, 11, 11);
        controlsLayout->setObjectName(QString::fromUtf8("controlsLayout"));
        controlsLayout->setContentsMargins(0, 0, 0, 0);
        testInfoButton = new QPushButton(controlsWidget);
        testInfoButton->setObjectName(QString::fromUtf8("testInfoButton"));
        testInfoButton->setMinimumSize(QSize(120, 35));
        testInfoButton->setStyleSheet(QString::fromUtf8("\n"
"              QPushButton {\n"
"                  background-color: #007acc;\n"
"                  border: none;\n"
"                  border-radius: 6px;\n"
"                  color: white;\n"
"                  font-size: 13px;\n"
"                  font-weight: 500;\n"
"                  padding: 8px 16px;\n"
"              }\n"
"              QPushButton:hover {\n"
"                  background-color: #005a9e;\n"
"              }\n"
"              QPushButton:pressed {\n"
"                  background-color: #004578;\n"
"              }\n"
"             "));

        controlsLayout->addWidget(testInfoButton);

        testWarningButton = new QPushButton(controlsWidget);
        testWarningButton->setObjectName(QString::fromUtf8("testWarningButton"));
        testWarningButton->setMinimumSize(QSize(120, 35));
        testWarningButton->setStyleSheet(QString::fromUtf8("\n"
"              QPushButton {\n"
"                  background-color: #f39c12;\n"
"                  border: none;\n"
"                  border-radius: 6px;\n"
"                  color: white;\n"
"                  font-size: 13px;\n"
"                  font-weight: 500;\n"
"                  padding: 8px 16px;\n"
"              }\n"
"              QPushButton:hover {\n"
"                  background-color: #e67e22;\n"
"              }\n"
"              QPushButton:pressed {\n"
"                  background-color: #d35400;\n"
"              }\n"
"             "));

        controlsLayout->addWidget(testWarningButton);

        testErrorButton = new QPushButton(controlsWidget);
        testErrorButton->setObjectName(QString::fromUtf8("testErrorButton"));
        testErrorButton->setMinimumSize(QSize(120, 35));
        testErrorButton->setStyleSheet(QString::fromUtf8("\n"
"              QPushButton {\n"
"                  background-color: #e74c3c;\n"
"                  border: none;\n"
"                  border-radius: 6px;\n"
"                  color: white;\n"
"                  font-size: 13px;\n"
"                  font-weight: 500;\n"
"                  padding: 8px 16px;\n"
"              }\n"
"              QPushButton:hover {\n"
"                  background-color: #c0392b;\n"
"              }\n"
"              QPushButton:pressed {\n"
"                  background-color: #a93226;\n"
"              }\n"
"             "));

        controlsLayout->addWidget(testErrorButton);

        controlsSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        controlsLayout->addItem(controlsSpacer);

        clearLogButton = new QPushButton(controlsWidget);
        clearLogButton->setObjectName(QString::fromUtf8("clearLogButton"));
        clearLogButton->setMinimumSize(QSize(100, 35));
        clearLogButton->setStyleSheet(QString::fromUtf8("\n"
"              QPushButton {\n"
"                  background-color: #6c757d;\n"
"                  border: none;\n"
"                  border-radius: 6px;\n"
"                  color: white;\n"
"                  font-size: 13px;\n"
"                  font-weight: 500;\n"
"                  padding: 8px 16px;\n"
"              }\n"
"              QPushButton:hover {\n"
"                  background-color: #5a6268;\n"
"              }\n"
"              QPushButton:pressed {\n"
"                  background-color: #495057;\n"
"              }\n"
"             "));

        controlsLayout->addWidget(clearLogButton);


        contentLayout->addWidget(controlsWidget);


        mainLayout->addWidget(contentWidget);

        QT_DClass->setCentralWidget(centralWidget);

        retranslateUi(QT_DClass);

        QMetaObject::connectSlotsByName(QT_DClass);
    } // setupUi

    void retranslateUi(QMainWindow *QT_DClass)
    {
        QT_DClass->setWindowTitle(QCoreApplication::translate("QT_DClass", "QT_D - Modern Interface", nullptr));
        titleIcon->setText(QCoreApplication::translate("QT_DClass", "D", nullptr));
        titleLabel->setText(QCoreApplication::translate("QT_DClass", "QT_D - Modern Interface", nullptr));
        minimizeButton->setText(QCoreApplication::translate("QT_DClass", "\342\210\222", nullptr));
        closeButton->setText(QCoreApplication::translate("QT_DClass", "\303\227", nullptr));
        logTextEdit->setPlaceholderText(QCoreApplication::translate("QT_DClass", "\346\227\245\345\277\227\350\276\223\345\207\272\345\260\206\346\230\276\347\244\272\345\234\250\350\277\231\351\207\214...", nullptr));
        testInfoButton->setText(QCoreApplication::translate("QT_DClass", "\346\265\213\350\257\225\344\277\241\346\201\257\346\227\245\345\277\227", nullptr));
        testWarningButton->setText(QCoreApplication::translate("QT_DClass", "\346\265\213\350\257\225\350\255\246\345\221\212\346\227\245\345\277\227", nullptr));
        testErrorButton->setText(QCoreApplication::translate("QT_DClass", "\346\265\213\350\257\225\351\224\231\350\257\257\346\227\245\345\277\227", nullptr));
        clearLogButton->setText(QCoreApplication::translate("QT_DClass", "\346\270\205\347\251\272\346\227\245\345\277\227", nullptr));
    } // retranslateUi

};

namespace Ui {
    class QT_DClass: public Ui_QT_DClass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_QT_D_H
