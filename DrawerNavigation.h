#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QPropertyAnimation>
#include <QEasingCurve>
#include <QGraphicsDropShadowEffect>
#include <QScrollArea>
#include <QFrame>

class DrawerNavigation : public QWidget
{
    Q_OBJECT

public:
    explicit DrawerNavigation(QWidget* parent = nullptr);
    ~DrawerNavigation();

    // 控制抽屉开关
    void openDrawer();
    void closeDrawer();
    void toggleDrawer();
    
    // 设置抽屉宽度
    void setDrawerWidth(int width);
    
    // 添加导航项
    void addNavigationItem(const QString& text, const QString& icon = "", QWidget* targetWidget = nullptr);
    void addSeparator();
    
    // 设置当前选中项
    void setCurrentItem(int index);

signals:
    void navigationItemClicked(int index, const QString& text);
    void drawerOpened();
    void drawerClosed();

protected:
    void paintEvent(QPaintEvent* event) override;
    bool eventFilter(QObject* obj, QEvent* event) override;

private slots:
    void onNavigationItemClicked();
    void onAnimationFinished();

private:
    void setupUI();
    void setupAnimations();
    void createOverlay();
    void updateDrawerPosition();
    
    // UI组件
    QWidget* m_drawerWidget;
    QWidget* m_overlayWidget;
    QScrollArea* m_scrollArea;
    QWidget* m_contentWidget;
    QVBoxLayout* m_contentLayout;
    QPushButton* m_closeButton;
    QLabel* m_titleLabel;
    
    // 动画
    QPropertyAnimation* m_slideAnimation;
    QPropertyAnimation* m_overlayAnimation;
    
    // 状态
    bool m_isOpen;
    int m_drawerWidth;
    int m_currentIndex;
    
    // 导航项
    QList<QPushButton*> m_navigationItems;
    QList<QWidget*> m_targetWidgets;
    
    // 样式
    QString m_itemStyle;
    QString m_selectedItemStyle;
};
