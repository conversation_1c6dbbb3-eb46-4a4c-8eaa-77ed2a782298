#pragma once

#include <QObject>
#include <QString>
#include <QColor>

class ThemeManager : public QObject
{
    Q_OBJECT

public:
    enum Theme {
        Dark = 0,
        Light = 1
    };

    static ThemeManager* instance();

    Theme currentTheme() const { return m_currentTheme; }
    void setTheme(Theme theme);
    void toggleTheme();

    QString getMainWindowStyle() const;
    QString getTitleBarStyle() const;
    QString getButtonStyle() const;
    QString getLogTextEditStyle() const;

signals:
    void themeChanged(Theme theme);

private:
    explicit ThemeManager(QObject* parent = nullptr);
    void updateColors();

    Theme m_currentTheme;

    QColor m_windowBackground;
    QColor m_windowBorder;
    QColor m_titleBarBackground;
    QColor m_buttonBackground;
    QColor m_buttonHover;
    QColor m_buttonPressed;
    QColor m_buttonText;
    QColor m_textColor;
    QColor m_logBackground;
    QColor m_logBorder;
    QColor m_logSelection;
    QColor m_scrollBarHandle;
    QColor m_scrollBarHandleHover;
    QColor m_scrollBarHandlePressed;

    static ThemeManager* s_instance;
};