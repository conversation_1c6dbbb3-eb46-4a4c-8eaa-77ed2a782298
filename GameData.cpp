#include "GameData.h"

GameData::GameData(dmsystem* dmObj)
    : obj(dmObj)
{
}

GameData::~GameData()
{
}

__int64 GameData::GetCharacterPointer()
{
    if (obj == nullptr)
    {
        return 0;
    }

    __int64  v1 = NULL;
    /*auto v2 = sub_game0x8720(pdm, v0);

    v1 = v2 ? v2 : sub_game0x8720(pdm, Base_人物指针);
    if (v1) { v1 = v1 - 0x20; }*/
    v1 = obj->ReadDword(BASE_ADDRESS) - 0x30;

    return v1;
}

int GameData::GetCharacterAction()
{
    if (obj == nullptr)
    {
        return -1;
    }

}

void GameData::GetCharacterPosition(float& x, float& y, float& z)
{
    if (obj == nullptr)
    {
        x = y = z = 0.0f;
        return;
    }

    try
    {
        // 获取人物指针
        __int64 characterPtr = GetCharacterPointer();
        if (characterPtr == 0)
        {
            x = y = z = 0.0f;
            return;
        }

        //// 使用offset.h中定义的坐标偏移地址（假设坐标偏移）
        //__int64 xOffset = 0x20; // 需要根据实际游戏调整
        //__int64 yOffset = 0x24; // 需要根据实际游戏调整
        //__int64 zOffset = 0x28; // 需要根据实际游戏调整

        //// 使用dmsystem的ReadDword方法读取4字节浮点数坐标
        //int xInt = obj->ReadDword(characterPtr + xOffset, 4);
        //int yInt = obj->ReadDword(characterPtr + yOffset, 4);
        //int zInt = obj->ReadDword(characterPtr + zOffset, 4);

        //// 将整数转换为浮点数
        //x = *reinterpret_cast<float*>(&xInt);
        //y = *reinterpret_cast<float*>(&yInt);
        //z = *reinterpret_cast<float*>(&zInt);
    }
    catch (...)
    {
        // 异常时设置为0
        x = y = z = 0.0f;
    }
}
