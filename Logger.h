#pragma once

#include <QString>
#include <QTextEdit>
#include <QDateTime>
#include <QColor>
#include <QMutex>
#include <QMutexLocker>
#include <QObject>
#include <memory>

class Logger : public QObject
{
    Q_OBJECT

public:
    enum class LogLevel {
        Info,
        Warning,
        Error,
        Success,
        Debug
    };

    static std::shared_ptr<Logger> getInstance();
    void setLogWidget(QTextEdit* textEdit);
    void info(const QString& message, const QString& module = "");
    void warning(const QString& message, const QString& module = "");
    void error(const QString& message, const QString& module = "");
    void success(const QString& message, const QString& module = "");
    void debug(const QString& message, const QString& module = "");
    void clear();
    void setTimestampEnabled(bool enabled);
    void setModuleEnabled(bool enabled);
    void setMaxLines(int maxLines);

private:
    explicit Logger(QObject* parent = nullptr);
    ~Logger() = default;

    void log(LogLevel level, const QString& message, const QString& module);
    QString getLevelColor(LogLevel level) const;
    QString getLevelIcon(LogLevel level) const;
    QString getLevelName(LogLevel level) const;
    QString formatTimestamp() const;
    void checkAndCleanupLogs();

private:
    static std::shared_ptr<Logger> m_instance;
    static QMutex m_instanceMutex;

    QTextEdit* m_logWidget;
    QMutex m_logMutex;
    
    bool m_timestampEnabled;
    bool m_moduleEnabled;
    int m_maxLines;
    int m_currentLines;

    static const QString INFO_COLOR;
    static const QString WARNING_COLOR;
    static const QString ERROR_COLOR;
    static const QString SUCCESS_COLOR;
    static const QString DEBUG_COLOR;
    static const QString TIMESTAMP_COLOR;
    static const QString MODULE_COLOR;
};

#define LOG_INFO_SIMPLE(msg) Logger::getInstance()->info(msg, "")
#define LOG_WARNING_SIMPLE(msg) Logger::getInstance()->warning(msg, "")
#define LOG_ERROR_SIMPLE(msg) Logger::getInstance()->error(msg, "")
#define LOG_SUCCESS_SIMPLE(msg) Logger::getInstance()->success(msg, "")
#define LOG_DEBUG_SIMPLE(msg) Logger::getInstance()->debug(msg, "")
