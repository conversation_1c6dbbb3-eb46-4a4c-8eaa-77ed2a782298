#pragma once

#include <QString>
#include <QTextEdit>
#include <QDateTime>
#include <QColor>
#include <QMutex>
#include <QMutexLocker>
#include <QObject>
#include <memory>

/**
 * @brief 现代化日志系统 - 2025年风格
 * 
 * 支持多种日志级别，具有现代化的视觉效果和线程安全特性
 */
class Logger : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 日志级别枚举
     */
    enum class LogLevel {
        Info,       // 信息日志 - 蓝色
        Warning,    // 警告日志 - 橙色
        Error,      // 错误日志 - 红色
        Success,    // 成功日志 - 绿色
        Debug       // 调试日志 - 灰色
    };

    /**
     * @brief 获取日志系统单例
     * @return Logger实例的智能指针
     */
    static std::shared_ptr<Logger> getInstance();

    /**
     * @brief 设置日志输出控件
     * @param textEdit 用于显示日志的QTextEdit控件
     */
    void setLogWidget(QTextEdit* textEdit);

    /**
     * @brief 输出信息日志
     * @param message 日志消息
     * @param module 模块名称（可选）
     */
    void info(const QString& message, const QString& module = "");

    /**
     * @brief 输出警告日志
     * @param message 日志消息
     * @param module 模块名称（可选）
     */
    void warning(const QString& message, const QString& module = "");

    /**
     * @brief 输出错误日志
     * @param message 日志消息
     * @param module 模块名称（可选）
     */
    void error(const QString& message, const QString& module = "");

    /**
     * @brief 输出成功日志
     * @param message 日志消息
     * @param module 模块名称（可选）
     */
    void success(const QString& message, const QString& module = "");

    /**
     * @brief 输出调试日志
     * @param message 日志消息
     * @param module 模块名称（可选）
     */
    void debug(const QString& message, const QString& module = "");

    /**
     * @brief 清空日志
     */
    void clear();

    /**
     * @brief 设置是否启用时间戳
     * @param enabled 是否启用
     */
    void setTimestampEnabled(bool enabled);

    /**
     * @brief 设置是否启用模块名显示
     * @param enabled 是否启用
     */
    void setModuleEnabled(bool enabled);

    /**
     * @brief 设置最大日志行数（超出时自动清理旧日志）
     * @param maxLines 最大行数，0表示不限制
     */
    void setMaxLines(int maxLines);

private:
    explicit Logger(QObject* parent = nullptr);
    ~Logger() = default;

    /**
     * @brief 输出日志的核心方法
     * @param level 日志级别
     * @param message 日志消息
     * @param module 模块名称
     */
    void log(LogLevel level, const QString& message, const QString& module);

    /**
     * @brief 获取日志级别对应的颜色
     * @param level 日志级别
     * @return 颜色字符串
     */
    QString getLevelColor(LogLevel level) const;

    /**
     * @brief 获取日志级别对应的图标
     * @param level 日志级别
     * @return 图标字符串
     */
    QString getLevelIcon(LogLevel level) const;

    /**
     * @brief 获取日志级别对应的名称
     * @param level 日志级别
     * @return 级别名称
     */
    QString getLevelName(LogLevel level) const;

    /**
     * @brief 格式化时间戳
     * @return 格式化的时间字符串
     */
    QString formatTimestamp() const;

    /**
     * @brief 检查并清理超出限制的日志行
     */
    void checkAndCleanupLogs();

private:
    static std::shared_ptr<Logger> m_instance;
    static QMutex m_instanceMutex;

    QTextEdit* m_logWidget;
    QMutex m_logMutex;
    
    bool m_timestampEnabled;
    bool m_moduleEnabled;
    int m_maxLines;
    int m_currentLines;

    // 现代化配色方案
    static const QString INFO_COLOR;
    static const QString WARNING_COLOR;
    static const QString ERROR_COLOR;
    static const QString SUCCESS_COLOR;
    static const QString DEBUG_COLOR;
    static const QString TIMESTAMP_COLOR;
    static const QString MODULE_COLOR;
};

// 便捷的全局日志宏
#define LOG_INFO(msg, ...) Logger::getInstance()->info(msg, ##__VA_ARGS__)
#define LOG_WARNING(msg, ...) Logger::getInstance()->warning(msg, ##__VA_ARGS__)
#define LOG_ERROR(msg, ...) Logger::getInstance()->error(msg, ##__VA_ARGS__)
#define LOG_SUCCESS(msg, ...) Logger::getInstance()->success(msg, ##__VA_ARGS__)
#define LOG_DEBUG(msg, ...) Logger::getInstance()->debug(msg, ##__VA_ARGS__)
