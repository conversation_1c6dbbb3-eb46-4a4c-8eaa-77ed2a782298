#ifndef MAINTHREAD_H
#define MAINTHREAD_H


#include "obj.h"




bool LoadDmSoft();
void CleanupDmSoft();

//class MainThread : public Thread
//{
//public:
//
//	MainThread();
//	virtual ~MainThread();
//	virtual void OnInit();
//	virtual void OnClose();
//	virtual void OnHotKey(const MSG& msg);
//	virtual void HelpAuto();
//	virtual void HelpLogCoord();
//	virtual void EndAuto();
//	virtual void Exite(bool _iscout = true);
//	static Variate* m_Variate;
//
//public:
//	static MainThread* pMain;
//
//	void get() { 
//		pMain = this;
//	}
//private:
//	bool InitMem(std::wstring &out);
//
//private:
//	dmsystem* m_pDm;
//	HotKey* m_pHotKey;
//
//	//std::unique_ptr<ScriptThread>	m_pScriptThread;
//	//std::unique_ptr<WorkScript>		m_pWorkScript;
//	//std::unique_ptr<otherThread>	m_potherThread;
//	//std::unique_ptr<RemoteControl>  m_RemoteControl;
//};
//
//
#endif // !MAINTHREAD_H
