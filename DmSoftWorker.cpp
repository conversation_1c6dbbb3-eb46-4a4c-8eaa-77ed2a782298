#include "DmSoftWorker.h"
#include "obj.h"
#include "MainThread.h"

DmSoftWorker::DmSoftWorker(QObject *parent)
    : QObject(parent)
{
}

DmSoftWorker::~DmSoftWorker()
{
}

void DmSoftWorker::initializeDmSoft()
{
    try {
        // 发送进度更新信号
        emit progressUpdate("Loading DmSoft...");
        
        // 在工作线程中执行耗时的LoadDmSoft操作
        bool success = LoadDmSoft();
        
        if (success) {
            emit progressUpdate("DmSoft loaded successfully");
        } else {
            emit progressUpdate("DmSoft loading failed");
        }
        
        // 发送完成信号
        emit dmSoftInitialized(success);
        
    } catch (...) {
        emit progressUpdate("Exception occurred during DmSoft loading");
        emit dmSoftInitialized(false);
    }
}
