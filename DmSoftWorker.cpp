#pragma execution_character_set("utf-8")

#include "DmSoftWorker.h"
#include "obj.h"
#include "MainThread.h"

DmSoftWorker::DmSoftWorker(QObject *parent)
    : QObject(parent)
{
}

DmSoftWorker::~DmSoftWorker()
{
}

void DmSoftWorker::initializeDmSoft()
{
    try {
        // 发送进度更新信号
        emit progressUpdate(QStringLiteral("Loading DmSoft..."));

        // 在工作线程中执行耗时的LoadDmSoft操作
        bool success = LoadDmSoft();

        if (success) {
            emit progressUpdate(QStringLiteral("DmSoft loaded successfully"));
        } else {
            emit progressUpdate(QStringLiteral("DmSoft loading failed"));
        }

        // 发送完成信号
        emit dmSoftInitialized(success);

    } catch (...) {
        emit progressUpdate(QStringLiteral("Exception occurred during DmSoft loading"));
        emit dmSoftInitialized(false);
    }
}
