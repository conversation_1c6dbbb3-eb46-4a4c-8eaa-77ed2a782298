﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  moc SideNavigation.h
  QT_D.cpp
  SideNavigation.cpp
  main.cpp
F:\A_C+\QT_D\QT_D\QT_D.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 SideNavigation.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(70,22): error C2011: “SideNavigation”:“class”类型重定义 (编译源文件 SideNavigation.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明 (编译源文件 SideNavigation.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(3,17): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(4,5): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(19,1): error C2550: “{ctor}”: 构造函数初始值设定项列表只能在构造函数定义中使用
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(20,5): error C3861: “setupUI”: 找不到标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(21,18): error C2065: “LogsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(21,5): error C3861: “switchToPage”: 找不到标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(22,1): warning C4508: “{ctor}”: 函数应返回一个值；假定“void”返回类型
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(24,18): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(25,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(26,1): warning C4508: “{dtor}”: 函数应返回一个值；假定“void”返回类型
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(28,28): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(29,1): error C2270: “getLogTextEdit”: 非成员函数上不允许修饰符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(30,12): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(33,22): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(35,5): error C2065: “m_mainLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(35,36): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(36,5): error C2065: “m_mainLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(37,5): error C2065: “m_mainLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(40,5): error C2065: “m_navigationPanel”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(41,5): error C2065: “m_navigationPanel”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(42,5): error C2065: “m_navigationPanel”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(44,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(44,35): error C2065: “m_navigationPanel”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(45,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(46,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(49,5): error C2065: “m_logsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(50,5): error C2065: “m_settingsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(51,5): error C2065: “m_debugButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(52,5): error C2065: “m_aboutButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(55,13): error C2065: “m_logsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(55,50): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(55,73): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(55,86): error C2065: “onLogsClicked”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(55,5): error C3861: “connect”: 找不到标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(56,13): error C2065: “m_settingsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(56,54): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(56,77): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(56,94): error C2065: “onSettingsClicked”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(56,5): error C3861: “connect”: 找不到标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(57,13): error C2065: “m_debugButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(57,51): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(57,74): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(57,88): error C2065: “onDebugClicked”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(57,5): error C3861: “connect”: 找不到标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(58,13): error C2065: “m_aboutButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(58,51): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(58,74): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(58,88): error C2065: “onAboutClicked”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(58,5): error C3861: “connect”: 找不到标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(61,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(61,28): error C2065: “m_logsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(62,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(62,28): error C2065: “m_settingsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(63,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(63,28): error C2065: “m_debugButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(64,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(64,28): error C2065: “m_aboutButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(65,5): error C2065: “m_navLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(67,5): error C2065: “m_mainLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(67,29): error C2065: “m_navigationPanel”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(70,5): error C2065: “m_stackedWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(73,5): error C2065: “m_logsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(74,47): error C2065: “m_logsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(81,5): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(82,5): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(93,5): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(94,5): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(95,27): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(98,5): error C2065: “m_settingsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(99,51): error C2065: “m_settingsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(112,5): error C2065: “m_debugPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(113,48): error C2065: “m_debugPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(126,5): error C2065: “m_aboutPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(127,48): error C2065: “m_aboutPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(140,5): error C2065: “m_stackedWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(140,32): error C2065: “m_settingsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(141,5): error C2065: “m_stackedWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(141,32): error C2065: “m_debugPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(142,5): error C2065: “m_stackedWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(142,32): error C2065: “m_aboutPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(144,5): error C2065: “m_mainLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(144,29): error C2065: “m_stackedWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(147,5): error C2065: “m_buttonStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(166,5): error C2065: “m_activeButtonStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(185,5): error C3861: “updateButtonStyles”: 找不到标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(188,22): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(188,34): error C2182: “switchToPage”: 非法使用“void”类型
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(188,35): error C2065: “PageIndex”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(188,45): error C2146: 语法错误: 缺少“)”(在标识符“page”的前面)
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(189,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(189,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(196,22): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(198,18): error C2065: “LogsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(201,22): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(203,18): error C2065: “SettingsPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(206,22): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(208,18): error C2065: “DebugPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(211,22): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(213,18): error C2065: “AboutPage”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(216,22): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(218,5): error C2065: “m_logsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(218,33): error C2065: “m_buttonStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(219,5): error C2065: “m_settingsButton”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(219,37): error C2065: “m_buttonStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(219,50): fatal error C1003: 错误计数超过 100；正在停止编译
F:\A_C+\QT_D\QT_D\SideNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(70,22): error C2011: “SideNavigation”:“class”类型重定义 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(70,22): error C2011: “SideNavigation”:“class”类型重定义 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(219,48): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(236,50): error C2664: “void QBoxLayout::addWidget(QWidget *,int,Qt::Alignment)”: 无法将参数 1 从“SideNavigation *”转换为“QWidget *”
F:\A_C+\QT_D\QT_D\QT_D.cpp(236,34): message : 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
D:\Qt\5.15.2\msvc2019\include\QtWidgets\qboxlayout.h(74,10): message : 参见“QBoxLayout::addWidget”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(239,48): error C2027: 使用了未定义类型“SideNavigation”
F:\A_C+\QT_D\QT_D\SideNavigation.h(11): message : 参见“SideNavigation”的声明
