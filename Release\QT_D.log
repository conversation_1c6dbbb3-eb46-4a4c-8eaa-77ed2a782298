﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  moc SkillDecisionEngine.h
  QT_D.cpp
  SkillDecisionEngine.cpp
c1xx : fatal error C1083: 无法打开源文件: “SkillDecisionEngine.cpp”: No such file or directory
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(17,55): error C2614: “MonsterInfo”: 非法的成员初始化:“distance”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(19,50): error C2614: “MonsterInfo”: 非法的成员初始化:“distance”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,15): error C2059: 语法错误:“)” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,24): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,19): error C2371: “name”: 重定义；不同的基类型 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(23): message : 参见“name”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,33): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,35): error C2440: “初始化”: 无法从“const char [1]”转换为“int” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,32): message : 没有使该转换得以执行的上下文 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,46): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,53): error C2059: 语法错误:“{” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,53): error C2143: 语法错误: 缺少“;”(在“{”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,53): error C2447: “{”: 缺少函数标题(是否是老式的形式表?) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(26,15): error C2059: 语法错误:“const” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,11): error C2371: “name”: 重定义；不同的基类型 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(23): message : 参见“name”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,16): error C2065: “n”: 未声明的标识符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,25): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,20): error C2374: “key”: 重定义；多次初始化 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25): message : 参见“key”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,24): error C2065: “k”: 未声明的标识符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,41): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,28): error C2374: “isReady”: 重定义；多次初始化 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25): message : 参见“isReady”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,36): error C2065: “ready”: 未声明的标识符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,43): error C2059: 语法错误:“{” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,43): error C2143: 语法错误: 缺少“;”(在“{”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(27,43): error C2447: “{”: 缺少函数标题(是否是老式的形式表?) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(28,1): error C2059: 语法错误:“}” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(28,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,13): error C2086: “QString name”: 重定义 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(23): message : 参见“name”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(36,21): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(36,102): error C2550: “SkillTemplate”: 构造函数初始值设定项列表只能在构造函数定义中使用 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(36,103): warning C4508: “SkillTemplate”: 函数应返回一个值；假定“void”返回类型 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(37,1): error C2059: 语法错误:“}” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(37,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(47,38): error C2614: “DecisionResult”: 非法的成员初始化:“skillName”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(47,53): error C2614: “DecisionResult”: 非法的成员初始化:“targetPos”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(57,34): error C2614: “ClusterInfo”: 非法的成员初始化:“monsterCount”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(74,53): error C2143: 语法错误: 缺少“;”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(74,53): warning C4228: 使用了非标准扩展: 忽略声明符列表中逗号后面的限定符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(75,54): error C2059: 语法错误:“)” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(75,55): error C2238: 意外的标记位于“;”之前 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(78,43): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(78,43): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(82,49): error C2327: “SkillDecisionEngine::QPointF”: 不是类型名称、静态或枚举数 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(82,68): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(82,68): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(97,5): error C2923: “QMap”: 对于参数“T”，“SkillTemplate”不是有效的 模板 类型变量 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(36): message : 参见“SkillTemplate”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(97,34): error C2955: “QMap”: 使用 类 模板 需要 模板 参数列表 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qabstractitemmodel.h(165): message : 参见“QMap”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(102,48): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(102,48): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(113,57): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(113,57): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(122,45): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(122,45): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\GameData.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(1317,23): error C2079: “skill”使用未定义的 struct“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.cpp(1321,32): error C2664: “void QVector<SkillInfo>::append(const T &)”: 无法将参数 1 从“int”转换为“const T &”
          with
          [
              T=SkillInfo
          ]
F:\A_C+\QT_D\QT_D\QT_D.cpp(1321,32): message : 原因如下: 无法从“int”转换为“const T”
          with
          [
              T=SkillInfo
          ]
F:\A_C+\QT_D\QT_D\QT_D.cpp(1321,32): message : 未定义类型 "SkillInfo" 的使用
F:\A_C+\QT_D\QT_D\QT_D.h(32,8): message : 参见“SkillInfo”的声明 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qvector.h(142,10): message : 参见“QVector<SkillInfo>::append”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(1345,73): error C2039: "skillName": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(40): message : 参见“DecisionResult”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1346,70): error C2039: "targetPos": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(40): message : 参见“DecisionResult”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1346,96): error C2039: "targetPos": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(40): message : 参见“DecisionResult”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1361,105): error C2664: “float SkillDecisionEngine::calculateDistance(const QPointF &,const int)”: 无法将参数 2 从“const QPointF”转换为“const int”
F:\A_C+\QT_D\QT_D\QT_D.cpp(1361,96): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(82,18): message : 参见“SkillDecisionEngine::calculateDistance”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(1373,36): error C2036: “const SkillInfo *”: 未知的大小
F:\A_C+\QT_D\QT_D\QT_D.cpp(1374,12): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1382,105): error C2664: “float SkillDecisionEngine::calculateDistance(const QPointF &,const int)”: 无法将参数 2 从“const QPointF”转换为“const int”
F:\A_C+\QT_D\QT_D\QT_D.cpp(1382,96): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(82,18): message : 参见“SkillDecisionEngine::calculateDistance”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(1397,32): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1398,32): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1399,32): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1407,65): error C2039: "skillName": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(40): message : 参见“DecisionResult”的声明
