﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  Logger.cpp
  ModernStyle.cpp
  QT_D.cpp
F:\A_C+\QT_D\QT_D\QT_D.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\Logger.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\ModernStyle.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 ModernStyle.cpp)
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\Logger.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 Logger.cpp)
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(4,42): error C4596: “PRIMARY_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(5,44): error C4596: “SECONDARY_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(6,28): error C4596: “ACCENT_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(7,45): error C4596: “BACKGROUND_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(8,42): error C4596: “SURFACE_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(9,39): error C4596: “TEXT_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(10,49): error C4596: “TEXT_SECONDARY_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(11,41): error C4596: “BORDER_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(12,40): error C4596: “HOVER_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(13,42): error C4596: “PRESSED_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(14,42): error C4596: “SUCCESS_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(15,42): error C4596: “WARNING_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(16,40): error C4596: “ERROR_COLOR”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(18,22): error C4596: “getMainWindowStyle”: 成员声明中的非法限定名
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(19,1): error C2686: 不能重载具有相同参数类型的静态和非静态成员函数
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(19,1): message : 可能是“QString ModernStyle::getMainWindowStyle(void)”
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(19,1): message : 或    “QString ModernStyle::getMainWindowStyle(void)”
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(20,5): error C2059: 语法错误:“return”
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(26,42): error C2238: 意外的标记位于“;”之前
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(29,22): error C2143: 语法错误: 缺少“;”(在“ModernStyle::getTitleBarStyle”的前面)
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(30,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(30,1): error C2556: “int ModernStyle::getTitleBarStyle(void)”: 重载函数与“QString ModernStyle::getTitleBarStyle(void)”只是在返回类型上不同
F:\A_C+\QT_D\QT_D\ModernStyle.h(18): message : 参见“ModernStyle::getTitleBarStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(29,22): error C2371: “ModernStyle::getTitleBarStyle”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.h(18): message : 参见“ModernStyle::getTitleBarStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(31,12): error C2064: 项不会计算为接受 1 个参数的函数
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(51,11): error C2597: 对非静态成员“ModernStyle::SECONDARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(5): message : 参见“ModernStyle::SECONDARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(51,28): error C2597: 对非静态成员“ModernStyle::BORDER_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(11): message : 参见“ModernStyle::BORDER_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(51,42): error C2597: 对非静态成员“ModernStyle::PRIMARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(4): message : 参见“ModernStyle::PRIMARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(51,57): error C2597: 对非静态成员“ModernStyle::TEXT_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(9): message : 参见“ModernStyle::TEXT_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(54,22): error C2143: 语法错误: 缺少“;”(在“ModernStyle::getButtonStyle”的前面)
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(54,36): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(54,1): error C2371: “QString”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(29): message : 参见“QString”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(55,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(55,1): error C2556: “int ModernStyle::getButtonStyle(void)”: 重载函数与“QString ModernStyle::getButtonStyle(void)”只是在返回类型上不同
F:\A_C+\QT_D\QT_D\ModernStyle.h(23): message : 参见“ModernStyle::getButtonStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(54,22): error C2371: “ModernStyle::getButtonStyle”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.h(23): message : 参见“ModernStyle::getButtonStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(56,12): error C2065: “QString”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(119,7): error C2039: "arg": 不是 "ModernStyle" 的成员
F:\A_C+\QT_D\QT_D\ModernStyle.h(9): message : 参见“ModernStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(119,11): error C2597: 对非静态成员“ModernStyle::PRIMARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(4): message : 参见“ModernStyle::PRIMARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(119,26): error C2597: 对非静态成员“ModernStyle::HOVER_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(12): message : 参见“ModernStyle::HOVER_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(119,39): error C2597: 对非静态成员“ModernStyle::PRESSED_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(13): message : 参见“ModernStyle::PRESSED_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(119,54): error C2597: 对非静态成员“ModernStyle::HOVER_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(12): message : 参见“ModernStyle::HOVER_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(119,67): error C2597: 对非静态成员“ModernStyle::ERROR_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(16): message : 参见“ModernStyle::ERROR_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(120,10): error C2597: 对非静态成员“ModernStyle::PRIMARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(4): message : 参见“ModernStyle::PRIMARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(120,25): error C2597: 对非静态成员“ModernStyle::WARNING_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(15): message : 参见“ModernStyle::WARNING_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(120,40): error C2597: 对非静态成员“ModernStyle::ERROR_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(16): message : 参见“ModernStyle::ERROR_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(123,22): error C2143: 语法错误: 缺少“;”(在“ModernStyle::getLogTextEditStyle”的前面)
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(123,41): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(123,1): error C2371: “QString”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(29): message : 参见“QString”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(124,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(124,1): error C2556: “int ModernStyle::getLogTextEditStyle(void)”: 重载函数与“QString ModernStyle::getLogTextEditStyle(void)”只是在返回类型上不同
F:\A_C+\QT_D\QT_D\ModernStyle.h(27): message : 参见“ModernStyle::getLogTextEditStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(123,22): error C2371: “ModernStyle::getLogTextEditStyle”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.h(27): message : 参见“ModernStyle::getLogTextEditStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(125,12): error C2065: “QString”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(137,7): error C2039: "arg": 不是 "ModernStyle" 的成员
F:\A_C+\QT_D\QT_D\ModernStyle.h(9): message : 参见“ModernStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(137,11): error C2597: 对非静态成员“ModernStyle::SURFACE_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(8): message : 参见“ModernStyle::SURFACE_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(137,26): error C2597: 对非静态成员“ModernStyle::BORDER_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(11): message : 参见“ModernStyle::BORDER_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(137,40): error C2597: 对非静态成员“ModernStyle::TEXT_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(9): message : 参见“ModernStyle::TEXT_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(137,52): error C2597: 对非静态成员“ModernStyle::PRIMARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(4): message : 参见“ModernStyle::PRIMARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(140,22): error C2143: 语法错误: 缺少“;”(在“ModernStyle::getScrollBarStyle”的前面)
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(140,39): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(140,1): error C2371: “QString”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(29): message : 参见“QString”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(141,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(141,1): error C2556: “int ModernStyle::getScrollBarStyle(void)”: 重载函数与“QString ModernStyle::getScrollBarStyle(void)”只是在返回类型上不同
F:\A_C+\QT_D\QT_D\ModernStyle.h(31): message : 参见“ModernStyle::getScrollBarStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(140,22): error C2371: “ModernStyle::getScrollBarStyle”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.h(31): message : 参见“ModernStyle::getScrollBarStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(142,12): error C2065: “QString”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(189,7): error C2039: "arg": 不是 "ModernStyle" 的成员
F:\A_C+\QT_D\QT_D\ModernStyle.h(9): message : 参见“ModernStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(189,11): error C2597: 对非静态成员“ModernStyle::SECONDARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(5): message : 参见“ModernStyle::SECONDARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(189,28): error C2597: 对非静态成员“ModernStyle::PRIMARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(4): message : 参见“ModernStyle::PRIMARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(189,43): error C2597: 对非静态成员“ModernStyle::SECONDARY_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(5): message : 参见“ModernStyle::SECONDARY_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(192,22): error C2143: 语法错误: 缺少“;”(在“ModernStyle::getCompleteStyle”的前面)
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(192,38): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(192,1): error C2371: “QString”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(29): message : 参见“QString”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(193,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(193,1): error C2556: “int ModernStyle::getCompleteStyle(void)”: 重载函数与“QString ModernStyle::getCompleteStyle(void)”只是在返回类型上不同
F:\A_C+\QT_D\QT_D\ModernStyle.h(35): message : 参见“ModernStyle::getCompleteStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(192,22): error C2371: “ModernStyle::getCompleteStyle”: 重定义；不同的基类型
F:\A_C+\QT_D\QT_D\ModernStyle.h(35): message : 参见“ModernStyle::getCompleteStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(195,12): error C2065: “getTitleBarStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(195,31): error C3867: “ModernStyle::getTitleBarStyle”: 非标准语法；请使用 "&" 来创建指向成员的指针
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(195,31): error C2568: “+”: 无法解析函数重载
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(195,31): message : 可能是“QString ModernStyle::getTitleBarStyle(void)”
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(196,12): error C2065: “getButtonStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(197,12): error C2065: “getLogTextEditStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(198,12): error C2065: “getScrollBarStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(199,12): error C2065: “QString”: 未声明的标识符
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(209,14): error C2039: "arg": 不是 "ModernStyle" 的成员
F:\A_C+\QT_D\QT_D\ModernStyle.h(9): message : 参见“ModernStyle”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(209,18): error C2597: 对非静态成员“ModernStyle::TEXT_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(9): message : 参见“ModernStyle::TEXT_COLOR”的声明
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(209,30): error C2597: 对非静态成员“ModernStyle::BACKGROUND_COLOR”的非法引用
F:\A_C+\QT_D\QT_D\ModernStyle.cpp(7): message : 参见“ModernStyle::BACKGROUND_COLOR”的声明
F:\A_C+\QT_D\QT_D\Logger.cpp(16,58): error C2440: “<function-style-cast>”: 无法从“Logger *”转换为“std::shared_ptr<Logger>”
F:\A_C+\QT_D\QT_D\Logger.cpp(16,58): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
F:\A_C+\QT_D\QT_D\Logger.cpp(137,16): error C2059: 语法错误:“.”
F:\A_C+\QT_D\QT_D\Logger.cpp(142,5): error C2059: 语法错误:“if”
F:\A_C+\QT_D\QT_D\Logger.cpp(142,47): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(142,47): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\Logger.cpp(149,13): error C2143: 语法错误: 缺少“;”(在“+=”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(149,13): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\Logger.cpp(153,19): error C2143: 语法错误: 缺少“;”(在“++”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(153,19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\Logger.cpp(156,14): error C3927: "->": 非函数声明符后不允许尾随返回类型
F:\A_C+\QT_D\QT_D\Logger.cpp(156,16): error C3613: “->”后缺少返回类型(假定为“int”)
F:\A_C+\QT_D\QT_D\Logger.cpp(156,24): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\Logger.cpp(156,16): error C2146: 语法错误: 缺少“;”(在标识符“setValue”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(159,26): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\Logger.cpp(162,34): error C2761: “processEvents”: 不允许重新声明成员
F:\A_C+\QT_D\QT_D\Logger.cpp(163,1): error C2059: 语法错误:“}”
F:\A_C+\QT_D\QT_D\Logger.cpp(163,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(166,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(166,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\Logger.cpp(182,40): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\Logger.cpp(183,9): error C2143: 语法错误: 缺少“;”(在“case”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(183,40): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\Logger.cpp(184,9): error C2143: 语法错误: 缺少“;”(在“case”的前面)
F:\A_C+\QT_D\QT_D\Logger.cpp(216,39): error C2065: “newLines”: 未声明的标识符
F:\A_C+\QT_D\QT_D\Logger.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\ModernStyle.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qbasictimer.h(88,48): error C3412: “QTypeInfo<ModernStyle::QBasicTimer>”: 不能在当前范围内专用化模板 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qtimer.h(58,1): error C2504: “ModernStyle::QObject”: 未定义基类 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qtimer.h(213,10): error C3668: “ModernStyle::QTimer::timerEvent”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qtimer.h(245,21): error C3254: “ModernStyle”: 类包含显式重写“setSingleShot”，但并不从包含函数声明的接口派生 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qtimer.h(245,21): error C2838: “setSingleShot”: 成员声明中的限定名称非法 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtWidgets\qdesktopwidget.h(118,28): error C3254: “ModernStyle”: 类包含显式重写“screenCount”，但并不从包含函数声明的接口派生 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtWidgets\qdesktopwidget.h(118,28): error C2838: “screenCount”: 成员声明中的限定名称非法 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(7,7): error C3254: “ModernStyle”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生
F:\A_C+\QT_D\QT_D\QT_D.cpp(7,7): error C2838: “{ctor}”: 成员声明中的限定名称非法
F:\A_C+\QT_D\QT_D\QT_D.cpp(21,8): error C3254: “ModernStyle”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生
F:\A_C+\QT_D\QT_D\QT_D.cpp(21,8): error C2838: “{dtor}”: 成员声明中的限定名称非法
F:\A_C+\QT_D\QT_D\QT_D.cpp(25,12): error C3254: “ModernStyle”: 类包含显式重写“initializeWindow”，但并不从包含函数声明的接口派生
F:\A_C+\QT_D\QT_D\QT_D.cpp(25,12): error C2838: “initializeWindow”: 成员声明中的限定名称非法
F:\A_C+\QT_D\QT_D\QT_D.cpp(41,12): error C3254: “ModernStyle”: 类包含显式重写“initializeLogger”，但并不从包含函数声明的接口派生
F:\A_C+\QT_D\QT_D\QT_D.cpp(41,12): error C2838: “initializeLogger”: 成员声明中的限定名称非法
F:\A_C+\QT_D\QT_D\QT_D.cpp(52,12): error C3254: “ModernStyle”: 类包含显式重写“connectSignals”，但并不从包含函数声明的接口派生
F:\A_C+\QT_D\QT_D\QT_D.cpp(52,12): error C2838: “connectSignals”: 成员声明中的限定名称非法
F:\A_C+\QT_D\QT_D\QT_D.cpp(65,12): error C3254: “ModernStyle”: 类包含显式重写“showWelcomeMessage”，但并不从包含函数声明的接口派生
F:\A_C+\QT_D\QT_D\QT_D.cpp(65,12): error C2838: “showWelcomeMessage”: 成员声明中的限定名称非法
F:\A_C+\QT_D\QT_D\QT_D.cpp(68,72): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(138,101): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(144,106): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(150,103): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(156,55): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(156): fatal error C1075: “{”: 未找到匹配令牌
