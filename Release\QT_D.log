﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  Logger.cpp
  QT_D.cpp
F:\A_C+\QT_D\QT_D\QT_D.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(211,101): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(212,1): error C3688: 文本后缀“Test”无效；未找到文文本运算符或文本运算符模板“operator """"Test”
F:\A_C+\QT_D\QT_D\QT_D.cpp(212,1): error C2143: 语法错误: 缺少“)”(在“}”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(212,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(217,106): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(218,1): error C3688: 文本后缀“Test”无效；未找到文文本运算符或文本运算符模板“operator """"Test”
F:\A_C+\QT_D\QT_D\QT_D.cpp(218,1): error C2143: 语法错误: 缺少“)”(在“}”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(218,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(223,103): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(224,1): error C3688: 文本后缀“Test”无效；未找到文文本运算符或文本运算符模板“operator """"Test”
F:\A_C+\QT_D\QT_D\QT_D.cpp(224,1): error C2143: 语法错误: 缺少“)”(在“}”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(224,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(229,55): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(230,1): error C3688: 文本后缀“Logger”无效；未找到文文本运算符或文本运算符模板“operator """"Logger”
F:\A_C+\QT_D\QT_D\QT_D.cpp(230,1): error C2143: 语法错误: 缺少“)”(在“}”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(230,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
D:\VS2019\VC\Tools\MSVC\14.29.30133\include\memory(1663,1): error C2440: “<function-style-cast>”: 无法从“_Ux *”转换为“std::shared_ptr<Logger>”
          with
          [
              _Ux=Logger
          ] (编译源文件 Logger.cpp)
D:\VS2019\VC\Tools\MSVC\14.29.30133\include\memory(1663,1): message : 无构造函数可以接受源类型，或构造函数重载决策不明确 (编译源文件 Logger.cpp)
F:\A_C+\QT_D\QT_D\Logger.cpp(20): message : 查看对正在编译的函数 模板 实例化“void std::shared_ptr<Logger>::reset<Logger>(_Ux *)”的引用
          with
          [
              _Ux=Logger
          ]
F:\A_C+\QT_D\QT_D\Logger.cpp(20): message : 查看对正在编译的函数 模板 实例化“void std::shared_ptr<Logger>::reset<Logger>(_Ux *)”的引用
          with
          [
              _Ux=Logger
          ]
