﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  QT_D.cpp
  SkillDecisionEngine.cpp
  main.cpp
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(23,45): error C2614: “MonsterInfo”: 非法的成员初始化:“distance”不是基或成员 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,50): error C2614: “MonsterInfo”: 非法的成员初始化:“distance”不是基或成员 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,34): error C2550: “SkillInfo”: 构造函数初始值设定项列表只能在构造函数定义中使用 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,35): warning C4508: “SkillInfo”: 函数应返回一个值；假定“void”返回类型 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,43): error C2550: “SkillInfo”: 构造函数初始值设定项列表只能在构造函数定义中使用 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,44): warning C4508: “SkillInfo”: 函数应返回一个值；假定“void”返回类型 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(34,1): error C2059: 语法错误:“}” (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(34,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(37,13): error C2086: “QString name”: 重定义 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(29): message : 参见“name”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(40,24): error C2550: “void __cdecl `dynamic initializer for 'description''(void)”: 构造函数初始值设定项列表只能在构造函数定义中使用 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42,21): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42,75): error C2550: “SkillTemplate”: 构造函数初始值设定项列表只能在构造函数定义中使用 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42,76): warning C4508: “SkillTemplate”: 函数应返回一个值；假定“void”返回类型 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(43,1): error C2059: 语法错误:“}” (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(43,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(63,21): error C2614: “ClusterInfo”: 非法的成员初始化:“monsterCount”不是基或成员 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(76,38): error C2923: “QVector”: 对于参数“T”，“SkillInfo”不是有效的 模板 类型变量 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(76,64): error C2955: “QVector”: 使用 类 模板 需要 模板 参数列表 (编译源文件 SkillDecisionEngine.cpp)
D:\Qt\5.15.2\msvc2019\include\QtGui\qtextdocument.h(71): message : 参见“QVector”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(76,58): error C2955: “QVector”: 使用 类 模板 需要 模板 参数列表 (编译源文件 SkillDecisionEngine.cpp)
D:\Qt\5.15.2\msvc2019\include\QtGui\qtextdocument.h(71): message : 参见“QVector”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(88,53): error C2143: 语法错误: 缺少“;”(在“&”的前面) (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(88,53): warning C4228: 使用了非标准扩展: 忽略声明符列表中逗号后面的限定符 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(89,54): error C2059: 语法错误:“)” (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(89,55): error C2238: 意外的标记位于“;”之前 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(92,43): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(92,43): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(108,5): error C2923: “QMap”: 对于参数“T”，“SkillTemplate”不是有效的 模板 类型变量 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42): message : 参见“SkillTemplate”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(108,34): error C2955: “QMap”: 使用 类 模板 需要 模板 参数列表 (编译源文件 SkillDecisionEngine.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qdatastream.h(61): message : 参见“QMap”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(112,41): error C2923: “QVector”: 对于参数“T”，“SkillInfo”不是有效的 模板 类型变量 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(112,76): error C2955: “QVector”: 使用 类 模板 需要 模板 参数列表 (编译源文件 SkillDecisionEngine.cpp)
D:\Qt\5.15.2\msvc2019\include\QtGui\qtextdocument.h(71): message : 参见“QVector”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(112,61): error C2955: “QVector”: 使用 类 模板 需要 模板 参数列表 (编译源文件 SkillDecisionEngine.cpp)
D:\Qt\5.15.2\msvc2019\include\QtGui\qtextdocument.h(71): message : 参见“QVector”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(113,48): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(113,48): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(124,57): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(124,57): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(8,1): error C2512: “QMap”: 没有合适的默认构造函数可用
D:\Qt\5.15.2\msvc2019\include\QtCore\qdatastream.h(61,37): message : 参见“QMap”的声明 (编译源文件 SkillDecisionEngine.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(18,5): error C3861: “setupDefaultSkills”: 找不到标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(19,20): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(20,1): error C2143: 语法错误: 缺少“)”(在“}”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(20,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(22,27): error C2039: "setupDefaultSkills": 不是 "SkillDecisionEngine" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(66): message : 参见“SkillDecisionEngine”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(25,5): error C2065: “m_skillDatabase”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(28,5): error C2065: “battoSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(28,23): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(29,5): error C2146: 语法错误: 缺少“;”(在标识符“battoSlash”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(29,5): error C2065: “battoSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(30,5): error C2065: “battoSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(31,5): error C2065: “battoSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(32,5): error C2065: “battoSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(33,5): error C2065: “battoSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(34,21): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(34,5): error C2065: “m_skillDatabase”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(37,5): error C2146: 语法错误: 缺少“]”(在标识符“normalAttack”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(37,25): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(39,5): error C2065: “normalAttack”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(40,5): error C2065: “normalAttack”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(41,5): error C2065: “normalAttack”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(42,5): error C2065: “normalAttack”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(43,21): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(43,5): error C2065: “m_skillDatabase”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(46,5): error C2146: 语法错误: 缺少“]”(在标识符“SkillTemplate”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(47,5): error C2065: “upperSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(48,5): error C2065: “upperSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(49,5): error C2065: “upperSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(50,5): error C2065: “upperSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(51,5): error C2065: “upperSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(52,5): error C2065: “upperSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(52,30): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(53,5): error C2146: 语法错误: 缺少“;”(在标识符“m_skillDatabase”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(53,5): error C2065: “m_skillDatabase”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(53,36): error C2065: “upperSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(56,19): error C2146: 语法错误: 缺少“;”(在标识符“ghostSlash”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(56,5): warning C4551: 缺少参数列表的函数调用
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(56,19): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(57,5): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(58,5): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(59,5): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(60,5): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(61,5): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(62,5): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(62,30): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(63,5): error C2146: 语法错误: 缺少“;”(在标识符“m_skillDatabase”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(63,5): error C2065: “m_skillDatabase”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(63,36): error C2065: “ghostSlash”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(66,5): error C2065: “waveSword”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(66,22): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(67,5): error C2146: 语法错误: 缺少“;”(在标识符“waveSword”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(67,5): error C2065: “waveSword”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(68,5): error C2065: “waveSword”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(69,5): error C2065: “waveSword”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(70,5): error C2065: “waveSword”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(71,5): error C2065: “waveSword”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(72,21): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(72,5): error C2065: “m_skillDatabase”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(74,10): error C2146: 语法错误: 缺少“]”(在标识符“debugInfo”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(74,28): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(77,28): error C2039: "calculateDistance": 不是 "SkillDecisionEngine" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(66): message : 参见“SkillDecisionEngine”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(84,95): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(84,95): error C2143: 语法错误: 缺少“,”(在“&”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(85,1): error C2511: “void SkillDecisionEngine::updateMonsterDistances(QVector<MonsterInfo> &,const int)”:“SkillDecisionEngine”中没有找到重载的成员函数
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(66): message : 参见“SkillDecisionEngine”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(87,17): error C2039: "distance": 不是 "MonsterInfo" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(17): message : 参见“MonsterInfo”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(87,46): error C2064: 项不会计算为接受 2 个参数的函数
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(87,77): error C2065: “playerPos”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(91,36): error C2039: "getSkillTemplate": 不是 "SkillDecisionEngine" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(66): message : 参见“SkillDecisionEngine”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(91,52): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(91,1): error C2365: “SkillTemplate”: 重定义；以前的定义是“函数”
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42): message : 参见“SkillTemplate”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(91,52): error C2146: 语法错误: 缺少“;”(在标识符“getSkillTemplate”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(92,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(92,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(104,35): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(110,55): error C2923: “QVector”: 对于参数“T”，“SkillInfo”不是有效的 模板 类型变量
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(110,81): error C2955: “QVector”: 使用 类 模板 需要 模板 参数列表
D:\Qt\5.15.2\msvc2019\include\QtGui\qtextdocument.h(71): message : 参见“QVector”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(110,75): error C2955: “QVector”: 使用 类 模板 需要 模板 参数列表
D:\Qt\5.15.2\msvc2019\include\QtGui\qtextdocument.h(71): message : 参见“QVector”的声明
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.cpp(110,81): fatal error C1003: 错误计数超过 100；正在停止编译
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(23,45): error C2614: “MonsterInfo”: 非法的成员初始化:“distance”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(25,50): error C2614: “MonsterInfo”: 非法的成员初始化:“distance”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,15): error C2059: 语法错误:“)” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,27): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,34): error C2059: 语法错误:“{” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,34): error C2143: 语法错误: 缺少“;”(在“{”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31,34): error C2447: “{”: 缺少函数标题(是否是老式的形式表?) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(32,15): error C2059: 语法错误:“const” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,17): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,11): error C2371: “name”: 重定义；不同的基类型 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(29): message : 参见“name”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,16): error C2065: “n”: 未声明的标识符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,25): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,24): error C2065: “k”: 未声明的标识符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,41): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,28): error C2374: “isReady”: 重定义；多次初始化 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(31): message : 参见“isReady”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,36): error C2065: “ready”: 未声明的标识符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,43): error C2059: 语法错误:“{” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,43): error C2143: 语法错误: 缺少“;”(在“{”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(33,43): error C2447: “{”: 缺少函数标题(是否是老式的形式表?) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(34,1): error C2059: 语法错误:“}” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(34,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(37,13): error C2086: “QString name”: 重定义 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(29): message : 参见“name”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42,21): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42,75): error C2550: “SkillTemplate”: 构造函数初始值设定项列表只能在构造函数定义中使用 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42,76): warning C4508: “SkillTemplate”: 函数应返回一个值；假定“void”返回类型 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(43,1): error C2059: 语法错误:“}” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(43,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(63,21): error C2614: “ClusterInfo”: 非法的成员初始化:“monsterCount”不是基或成员 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(88,53): error C2143: 语法错误: 缺少“;”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(88,53): warning C4228: 使用了非标准扩展: 忽略声明符列表中逗号后面的限定符 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(89,54): error C2059: 语法错误:“)” (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(89,55): error C2238: 意外的标记位于“;”之前 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(92,43): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(92,43): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(108,5): error C2923: “QMap”: 对于参数“T”，“SkillTemplate”不是有效的 模板 类型变量 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(42): message : 参见“SkillTemplate”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(108,34): error C2955: “QMap”: 使用 类 模板 需要 模板 参数列表 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qabstractitemmodel.h(165): message : 参见“QMap”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(113,48): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(113,48): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(124,57): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(124,57): error C2143: 语法错误: 缺少“,”(在“&”的前面) (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\GameData.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(1317,23): error C2079: “skill”使用未定义的 struct“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.cpp(1321,32): error C2664: “void QVector<SkillInfo>::append(const T &)”: 无法将参数 1 从“int”转换为“const T &”
          with
          [
              T=SkillInfo
          ]
F:\A_C+\QT_D\QT_D\QT_D.cpp(1321,32): message : 原因如下: 无法从“int”转换为“const T”
          with
          [
              T=SkillInfo
          ]
F:\A_C+\QT_D\QT_D\QT_D.cpp(1321,32): message : 未定义类型 "SkillInfo" 的使用
F:\A_C+\QT_D\QT_D\QT_D.h(32,8): message : 参见“SkillInfo”的声明 (编译源文件 QT_D.cpp)
D:\Qt\5.15.2\msvc2019\include\QtCore\qvector.h(142,10): message : 参见“QVector<SkillInfo>::append”的声明 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(1345,73): error C2039: "skillName": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(46): message : 参见“DecisionResult”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1346,70): error C2039: "targetPos": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(46): message : 参见“DecisionResult”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1346,96): error C2039: "targetPos": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(46): message : 参见“DecisionResult”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1361,47): error C2039: "calculateDistance": 不是 "SkillDecisionEngine" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(66): message : 参见“SkillDecisionEngine”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1361,64): error C3861: “calculateDistance”: 找不到标识符
F:\A_C+\QT_D\QT_D\QT_D.cpp(1373,36): error C2036: “const SkillInfo *”: 未知的大小
F:\A_C+\QT_D\QT_D\QT_D.cpp(1374,12): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1382,47): error C2039: "calculateDistance": 不是 "SkillDecisionEngine" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(66): message : 参见“SkillDecisionEngine”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1382,64): error C3861: “calculateDistance”: 找不到标识符
F:\A_C+\QT_D\QT_D\QT_D.cpp(1397,32): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1398,32): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1399,32): error C2027: 使用了未定义类型“SkillInfo”
F:\A_C+\QT_D\QT_D\QT_D.h(32): message : 参见“SkillInfo”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(1407,65): error C2039: "skillName": 不是 "DecisionResult" 的成员
F:\A_C+\QT_D\QT_D\SkillDecisionEngine.h(46): message : 参见“DecisionResult”的声明
