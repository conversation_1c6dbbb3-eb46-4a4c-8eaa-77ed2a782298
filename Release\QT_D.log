﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  moc QT_D.h
  QT_D.cpp
  main.cpp
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
  moc_QT_D.cpp
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
QT_D.obj : error LNK2001: 无法解析的外部符号 "public: static struct QMetaObject const DmSoftWorker::staticMetaObject" (?staticMetaObject@DmSoftWorker@@2UQMetaObject@@B)
QT_D.obj : error LNK2001: 无法解析的外部符号 "public: void __thiscall DmSoftWorker::progressUpdate(class QString const &)" (?progressUpdate@DmSoftWorker@@QAEXABVQString@@@Z)
QT_D.obj : error LNK2001: 无法解析的外部符号 "public: void __thiscall DmSoftWorker::dmSoftInitialized(bool)" (?dmSoftInitialized@DmSoftWorker@@QAEX_N@Z)
QT_D.obj : error LNK2001: 无法解析的外部符号 "public: void __thiscall DmSoftWorker::initializeDmSoft(void)" (?initializeDmSoft@DmSoftWorker@@QAEXXZ)
QT_D.obj : error LNK2001: 无法解析的外部符号 "public: __thiscall DmSoftWorker::DmSoftWorker(class QObject *)" (??0DmSoftWorker@@QAE@PAVQObject@@@Z)
F:\A_C+\QT_D\QT_D\Release\QT_D.exe : fatal error LNK1120: 5 个无法解析的外部命令
