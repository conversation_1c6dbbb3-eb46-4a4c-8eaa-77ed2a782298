﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  DrawerNavigation.cpp
  QT_D.cpp
F:\A_C+\QT_D\QT_D\QT_D.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 DrawerNavigation.cpp)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(17,7): error C2614: “DrawerNavigation”: 非法的成员初始化:“m_isOpen”不是基或成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(69,28): error C2039: "size": 不是 "QObject" 的成员
D:\Qt\5.15.2\msvc2019\include\QtCore\qcoreevent.h(347): message : 参见“QObject”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(166,24): error C2039: "openDrawer": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(168,9): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(170,5): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(171,5): error C3861: “show”: 找不到标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(172,5): error C2065: “m_overlayWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(174,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(174,45): error C2065: “m_drawerWidth”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(175,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(176,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(178,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(179,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(180,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(185,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(187,5): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(200,9): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(218,24): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(220,47): error C2065: “m_contentWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(221,25): error C2065: “m_itemStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(228,42): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(228,90): error C2248: “DrawerNavigation::onNavigationItemClicked”: 无法访问 private 成员(在“DrawerNavigation”类中声明)
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(44): message : 参见“DrawerNavigation::onNavigationItemClicked”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(228,5): error C3861: “connect”: 找不到标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(230,5): error C2065: “m_contentLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(230,35): error C2065: “m_contentLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(232,5): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(233,5): error C2065: “m_targetWidgets”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(250,24): error C2039: "setCurrentItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(252,31): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,30): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,30): error C2672: “begin”: 未找到匹配的重载函数
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): error C2893: 未能使函数模板“unknown-type std::begin(_Container &)”专用化
D:\VS2019\VC\Tools\MSVC\14.29.30133\include\xutility(1844): message : 参见“std::begin”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): message : 用下列模板参数:
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): message : “_Container=unknown-type”
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): error C2784: “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 未能从“unknown-type”为“std::initializer_list<_Elem>”推导 模板 参数
D:\VS2019\VC\Tools\MSVC\14.29.30133\include\initializer_list(55): message : 参见“std::begin”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,30): error C2672: “end”: 未找到匹配的重载函数
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): error C2893: 未能使函数模板“unknown-type std::end(_Container &)”专用化
D:\VS2019\VC\Tools\MSVC\14.29.30133\include\xutility(1854): message : 参见“std::end”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): message : 用下列模板参数:
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): message : “_Container=unknown-type”
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): error C2784: “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 未能从“unknown-type”为“std::initializer_list<_Elem>”推导 模板 参数
D:\VS2019\VC\Tools\MSVC\14.29.30133\include\initializer_list(60): message : 参见“std::end”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): error C3536: “<begin>$L0”: 初始化之前无法使用
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): error C3536: “<end>$L0”: 初始化之前无法使用
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,47): error C2100: 非法的间接寻址
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,49): error C2440: “初始化”: 无法从“int”转换为“QPushButton *”
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,47): message : 从整型强制转换为指针类型要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(255,29): error C2065: “m_itemStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(258,31): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(259,9): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(259,49): error C2065: “m_selectedItemStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(260,9): error C2065: “m_currentIndex”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(269,17): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(279,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(290,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(310,5): error C2084: 函数“DrawerNavigation::DrawerNavigation(QWidget *)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(6): message : 参见“{ctor}”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(320,7): error C2614: “DrawerNavigation”: 非法的成员初始化:“m_isOpen”不是基或成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(368,1): error C2084: 函数“DrawerNavigation::~DrawerNavigation(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(63): message : 参见“{dtor}”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(372,1): error C2084: 函数“void DrawerNavigation::setupUI(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(67): message : 参见“setupUI”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(476,1): error C2084: 函数“void DrawerNavigation::setupAnimations(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(143): message : 参见“setupAnimations”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(492,1): error C2084: 函数“void DrawerNavigation::createOverlay(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(157): message : 参见“createOverlay”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(500,24): error C2039: "openDrawer": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(500,34): error C2084: 函数“void openDrawer(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(166): message : 参见“openDrawer”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(502,9): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(504,5): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(505,5): error C3861: “show”: 找不到标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(506,5): error C2065: “m_overlayWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(509,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(509,45): error C2065: “m_drawerWidth”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(510,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(511,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(514,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(515,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(516,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(520,1): error C2084: 函数“void DrawerNavigation::closeDrawer(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(183): message : 参见“closeDrawer”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(521,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(523,5): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(536,1): error C2084: 函数“void DrawerNavigation::toggleDrawer(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(198): message : 参见“toggleDrawer”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(537,9): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(538,9): error C2065: “closeDrawer”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(540,9): error C2065: “openDrawer”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(545,1): error C2084: 函数“void DrawerNavigation::setDrawerWidth(int)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(207): message : 参见“setDrawerWidth”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(555,24): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(555,41): error C2084: 函数“void addNavigationItem(const QString &,const QString &,QWidget *)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(218): message : 参见“addNavigationItem”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(557,47): error C2065: “m_contentWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(558,25): error C2065: “m_itemStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(567,42): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(567,90): error C2248: “DrawerNavigation::onNavigationItemClicked”: 无法访问 private 成员(在“DrawerNavigation”类中声明)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(264): message : 参见“DrawerNavigation::onNavigationItemClicked”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(567,5): error C3861: “connect”: 找不到标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(570,5): error C2065: “m_contentLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(570,35): error C2065: “m_contentLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(573,5): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(574,5): error C2065: “m_targetWidgets”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(578,1): error C2084: 函数“void DrawerNavigation::addSeparator(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(236): message : 参见“addSeparator”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(591,24): error C2039: "setCurrentItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(591,38): error C2084: 函数“void setCurrentItem(int)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(250): message : 参见“setCurrentItem”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(593,31): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(596,9): error C2065: “item”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(596,29): error C2065: “m_itemStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(600,5): error C2059: 语法错误:“if”
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(600,57): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(600,57): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(604,1): error C2059: 语法错误:“}”
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(604,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(607,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(607,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(620,1): error C2084: 函数“void DrawerNavigation::onAnimationFinished(void)”已有主体
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(277): message : 参见“onAnimationFinished”的前一个定义
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(621,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(621,18): fatal error C1003: 错误计数超过 100；正在停止编译
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(224,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(225,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(226,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(228,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(229,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(231,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(232,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(239,36): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(240,5): error C2146: 语法错误: 缺少“)”(在标识符“m_menuButton”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(240,5): error C2146: 语法错误: 缺少“;”(在标识符“m_menuButton”的前面)
