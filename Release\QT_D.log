﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  DrawerNavigation.cpp
  QT_D.cpp
  main.cpp
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 DrawerNavigation.cpp)
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(17,7): error C2614: “DrawerNavigation”: 非法的成员初始化:“m_isOpen”不是基或成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(197,24): error C2039: "openDrawer": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(199,9): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(201,5): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(202,5): error C3861: “show”: 找不到标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(203,5): error C2065: “m_overlayWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(206,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(206,45): error C2065: “m_drawerWidth”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(207,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(208,5): error C2065: “m_slideAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(211,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(212,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(213,5): error C2065: “m_overlayAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(218,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(220,5): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(234,9): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(252,24): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(254,47): error C2065: “m_contentWidget”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(255,25): error C2065: “m_itemStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(264,42): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(264,90): error C2248: “DrawerNavigation::onNavigationItemClicked”: 无法访问 private 成员(在“DrawerNavigation”类中声明)
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(44): message : 参见“DrawerNavigation::onNavigationItemClicked”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(264,5): error C3861: “connect”: 找不到标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(267,5): error C2065: “m_contentLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(267,35): error C2065: “m_contentLayout”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(270,5): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(271,5): error C2065: “m_targetWidgets”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(288,24): error C2039: "setCurrentItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(290,31): error C2065: “m_navigationItems”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(293,9): error C2065: “item”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(293,29): error C2065: “m_itemStyle”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(297,5): error C2059: 语法错误:“if”
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(297,57): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(297,57): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(301,1): error C2059: 语法错误:“}”
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(301,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(304,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(304,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(318,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(329,10): error C2065: “m_isOpen”: 未声明的标识符
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(339,24): error C2601: “DrawerNavigation::eventFilter”: 本地函数定义是非法的
F:\A_C+\QT_D\QT_D\DrawerNavigation.cpp(347,1): fatal error C1004: 发现意外的文件尾
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(224,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(225,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(226,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(228,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(229,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(231,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(232,25): error C2039: "addNavigationItem": 不是 "DrawerNavigation" 的成员
F:\A_C+\QT_D\QT_D\DrawerNavigation.h(14): message : 参见“DrawerNavigation”的声明
F:\A_C+\QT_D\QT_D\QT_D.cpp(232,51): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(235,5): error C2146: 语法错误: 缺少“)”(在标识符“connect”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(235,5): error C2146: 语法错误: 缺少“;”(在标识符“connect”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(239,36): error C2001: 常量中有换行符
F:\A_C+\QT_D\QT_D\QT_D.cpp(240,5): error C2146: 语法错误: 缺少“)”(在标识符“m_menuButton”的前面)
F:\A_C+\QT_D\QT_D\QT_D.cpp(240,5): error C2146: 语法错误: 缺少“;”(在标识符“m_menuButton”的前面)
