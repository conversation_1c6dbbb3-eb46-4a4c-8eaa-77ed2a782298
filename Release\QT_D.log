﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  moc QT_D.h
  moc SideNavigation.h
  QT_D.cpp
  SideNavigation.cpp
  main.cpp
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 SideNavigation.cpp)
F:\A_C+\QT_D\QT_D\QT_D.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(18,7): error C2614: “SideNavigation”: 非法的成员初始化:“m_logTextEdit”不是基或成员
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(129,5): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(140,5): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(141,5): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(142,27): error C2065: “m_logTextEdit”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(226,9): error C2046: 非法的 case
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(229,9): error C2046: 非法的 case
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(232,9): error C2046: 非法的 case
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(235,9): error C2046: 非法的 case
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(228,13): error C2043: 非法 break
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(231,13): error C2043: 非法 break
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(234,13): error C2043: 非法 break
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(237,13): error C2043: 非法 break
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(239,1): error C2059: 语法错误:“}”
F:\A_C+\QT_D\QT_D\SideNavigation.cpp(239,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
F:\A_C+\QT_D\QT_D\SideNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 QT_D.cpp)
F:\A_C+\QT_D\QT_D\SideNavigation.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 main.cpp)
F:\A_C+\QT_D\QT_D\QT_D.cpp(239,50): error C2039: "getLogTextEdit": 不是 "SideNavigation" 的成员
F:\A_C+\QT_D\QT_D\SideNavigation.h(12): message : 参见“SideNavigation”的声明
