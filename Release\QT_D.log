﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.15.2
  moc SmoothScrollBar.h
  Logger.cpp
  SmoothScrollBar.cpp
  SmoothTextEdit.cpp
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_C+\QT_D\QT_D\SmoothScrollBar.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 SmoothScrollBar.cpp)
F:\A_C+\QT_D\QT_D\SmoothScrollBar.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 SmoothTextEdit.cpp)
F:\A_C+\QT_D\QT_D\SmoothScrollBar.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 Logger.cpp)
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(155,5): error C3861: “smoothScrollTo”: 找不到标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(158,23): error C2039: "smoothScrollTo": 不是 "SmoothScrollBar" 的成员
F:\A_C+\QT_D\QT_D\SmoothScrollBar.h(9): message : 参见“SmoothScrollBar”的声明
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(160,10): error C2065: “m_animationEnabled”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(161,21): error C2352: “QAbstractSlider::setValue”: 非静态成员函数的非法调用
D:\Qt\5.15.2\msvc2019\include\QtWidgets\qabstractslider.h(118): message : 参见“QAbstractSlider::setValue”的声明
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(165,5): error C2065: “m_targetValue”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(165,28): error C3861: “minimum”: 找不到标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(165,46): error C3861: “maximum”: 找不到标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(167,9): error C2065: “m_scrollAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(168,9): error C2065: “m_scrollAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(171,5): error C2065: “m_scrollAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(171,38): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(172,5): error C2065: “m_scrollAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(172,36): error C2065: “m_targetValue”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothScrollBar.cpp(173,5): error C2065: “m_scrollAnimation”: 未声明的标识符
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(17,22): error C2601: “SmoothTextEdit::setupSmoothScrollBars”: 本地函数定义是非法的
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(14): message : 此行有一个“{”没有匹配项
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(30,22): error C2601: “SmoothTextEdit::applyLogTextStyle”: 本地函数定义是非法的
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(14): message : 此行有一个“{”没有匹配项
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(47,22): error C2601: “SmoothTextEdit::setScrollAnimationDuration”: 本地函数定义是非法的
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(14): message : 此行有一个“{”没有匹配项
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(57,22): error C2601: “SmoothTextEdit::setScrollEasingCurve”: 本地函数定义是非法的
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(14): message : 此行有一个“{”没有匹配项
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(67,22): error C2601: “SmoothTextEdit::wheelEvent”: 本地函数定义是非法的
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(14): message : 此行有一个“{”没有匹配项
F:\A_C+\QT_D\QT_D\SmoothTextEdit.cpp(14): fatal error C1075: “{”: 未找到匹配令牌
