// 简单的编译测试文件
#include "SkillDecisionEngine.h"
#include <iostream>

int main()
{
    std::cout << "开始编译测试..." << std::endl;
    
    // 测试结构体创建
    MonsterInfo monster(100, 200, "测试怪物", 1);
    SkillInfo skill("拔刀斩", "Q", true);
    
    std::cout << "怪物位置: (" << monster.x << ", " << monster.y << ")" << std::endl;
    std::cout << "技能名称: " << skill.name.toStdString() << std::endl;
    
    // 测试距离计算
    QPointF p1(0, 0);
    QPointF p2(3, 4);
    float distance = SkillDecisionEngine::calculateDistance(p1, p2);
    std::cout << "距离计算测试: " << distance << " (应该是5)" << std::endl;
    
    std::cout << "✅ 编译测试成功！" << std::endl;
    return 0;
}
