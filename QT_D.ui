<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QT_DClass</class>
 <widget class="QMainWindow" name="QT_DClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>480</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>700</width>
    <height>400</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>QT_D - Modern Interface</string>
  </property>
  <property name="windowFlags">
   <set>Qt::FramelessWindowHint</set>
  </property>
  <property name="styleSheet">
   <string notr="true">
    QMainWindow {
        background-color: #1e1e1e;
        border: 1px solid #3c3c3c;
        border-radius: 8px;
    }
   </string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <property name="styleSheet">
    <string notr="true">
     QWidget {
         background-color: transparent;
     }
    </string>
   </property>
   <layout class="QVBoxLayout" name="mainLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>1</number>
    </property>
    <property name="topMargin">
     <number>1</number>
    </property>
    <property name="rightMargin">
     <number>1</number>
    </property>
    <property name="bottomMargin">
     <number>1</number>
    </property>
    <item>
     <widget class="QWidget" name="titleBar" native="true">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>40</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>40</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">
        QWidget#titleBar {
            background-color: #2d2d2d;
            border-top-left-radius: 7px;
            border-top-right-radius: 7px;
            border-bottom: 1px solid #3c3c3c;
        }
       </string>
      </property>
      <layout class="QHBoxLayout" name="titleBarLayout">
       <property name="spacing">
        <number>10</number>
       </property>
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QLabel" name="titleIcon">
         <property name="minimumSize">
          <size>
           <width>24</width>
           <height>24</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>24</width>
           <height>24</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">
           QLabel {
               background-color: #007acc;
               border-radius: 12px;
               color: white;
               font-weight: bold;
               font-size: 12px;
           }
          </string>
         </property>
         <property name="text">
          <string>D</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="titleLabel">
         <property name="styleSheet">
          <string notr="true">
           QLabel {
               color: #ffffff;
               font-size: 14px;
               font-weight: 500;
               background-color: transparent;
           }
          </string>
         </property>
         <property name="text">
          <string>QT_D - Modern Interface</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="titleSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="minimizeButton">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">
           QPushButton {
               background-color: transparent;
               border: none;
               border-radius: 15px;
               color: #ffffff;
               font-size: 16px;
               font-weight: bold;
           }
           QPushButton:hover {
               background-color: #404040;
           }
           QPushButton:pressed {
               background-color: #505050;
           }
          </string>
         </property>
         <property name="text">
          <string>−</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="closeButton">
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">
           QPushButton {
               background-color: transparent;
               border: none;
               border-radius: 15px;
               color: #ffffff;
               font-size: 14px;
               font-weight: bold;
           }
           QPushButton:hover {
               background-color: #e74c3c;
           }
           QPushButton:pressed {
               background-color: #c0392b;
           }
          </string>
         </property>
         <property name="text">
          <string>×</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QWidget" name="contentWidget" native="true">
      <property name="styleSheet">
       <string notr="true">
        QWidget#contentWidget {
            background-color: #1e1e1e;
            border-bottom-left-radius: 7px;
            border-bottom-right-radius: 7px;
        }
       </string>
      </property>
      <layout class="QVBoxLayout" name="contentLayout">
       <property name="spacing">
        <number>15</number>
       </property>
       <property name="leftMargin">
        <number>20</number>
       </property>
       <property name="topMargin">
        <number>20</number>
       </property>
       <property name="rightMargin">
        <number>20</number>
       </property>
       <property name="bottomMargin">
        <number>20</number>
       </property>
       <item>
        <widget class="QTextEdit" name="logTextEdit">
         <property name="styleSheet">
          <string notr="true">
           QTextEdit {
               background-color: #252525;
               border: 1px solid #3c3c3c;
               border-radius: 6px;
               color: #ffffff;
               font-family: 'Consolas', 'Monaco', monospace;
               font-size: 12px;
               padding: 10px;
               selection-background-color: #007acc;
           }
           QScrollBar:vertical {
               background-color: #2d2d2d;
               width: 12px;
               border-radius: 6px;
           }
           QScrollBar::handle:vertical {
               background-color: #555555;
               border-radius: 6px;
               min-height: 20px;
           }
           QScrollBar::handle:vertical:hover {
               background-color: #666666;
           }
           QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
               border: none;
               background: none;
           }
          </string>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="placeholderText">
          <string>日志输出将显示在这里...</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="controlsWidget" native="true">
         <layout class="QHBoxLayout" name="controlsLayout">
          <property name="spacing">
           <number>15</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <spacer name="controlsSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="clearLogButton">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>35</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">
              QPushButton {
                  background-color: #6c757d;
                  border: none;
                  border-radius: 6px;
                  color: white;
                  font-size: 13px;
                  font-weight: 500;
                  padding: 8px 16px;
              }
              QPushButton:hover {
                  background-color: #5a6268;
              }
              QPushButton:pressed {
                  background-color: #495057;
              }
             </string>
            </property>
            <property name="text">
             <string>清空日志</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="QT_D.qrc"/>
 </resources>
 <connections/>
</ui>
