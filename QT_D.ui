<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QT_DClass</class>
 <widget class="QMainWindow" name="QT_DClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QT_D</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <widget class="QRadioButton" name="radioButton">
    <property name="geometry">
     <rect>
      <x>140</x>
      <y>130</y>
      <width>95</width>
      <height>19</height>
     </rect>
    </property>
    <property name="text">
     <string>RadioButton</string>
    </property>
   </widget>
   <widget class="QToolButton" name="toolButton">
    <property name="geometry">
     <rect>
      <x>200</x>
      <y>80</y>
      <width>131</width>
      <height>31</height>
     </rect>
    </property>
    <property name="text">
     <string>...</string>
    </property>
   </widget>
   <widget class="QPushButton" name="pushButton">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>180</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>PushButton</string>
    </property>
   </widget>
   <widget class="QCommandLinkButton" name="commandLinkButton">
    <property name="geometry">
     <rect>
      <x>260</x>
      <y>210</y>
      <width>185</width>
      <height>41</height>
     </rect>
    </property>
    <property name="text">
     <string>CommandLinkButton</string>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>600</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="QT_D.qrc"/>
 </resources>
 <connections/>
</ui>
