/****************************************************************************
** Meta object code from reading C++ file 'QT_D.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../QT_D.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'QT_D.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QT_D_t {
    QByteArrayData data[10];
    char stringdata0[151];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QT_D_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QT_D_t qt_meta_stringdata_QT_D = {
    {
QT_MOC_LITERAL(0, 0, 4), // "QT_D"
QT_MOC_LITERAL(1, 5, 17), // "onMinimizeClicked"
QT_MOC_LITERAL(2, 23, 0), // ""
QT_MOC_LITERAL(3, 24, 14), // "onCloseClicked"
QT_MOC_LITERAL(4, 39, 20), // "onThemeToggleClicked"
QT_MOC_LITERAL(5, 60, 14), // "onThemeChanged"
QT_MOC_LITERAL(6, 75, 17), // "onTestInfoClicked"
QT_MOC_LITERAL(7, 93, 20), // "onTestWarningClicked"
QT_MOC_LITERAL(8, 114, 18), // "onTestErrorClicked"
QT_MOC_LITERAL(9, 133, 17) // "onClearLogClicked"

    },
    "QT_D\0onMinimizeClicked\0\0onCloseClicked\0"
    "onThemeToggleClicked\0onThemeChanged\0"
    "onTestInfoClicked\0onTestWarningClicked\0"
    "onTestErrorClicked\0onClearLogClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QT_D[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   54,    2, 0x08 /* Private */,
       3,    0,   55,    2, 0x08 /* Private */,
       4,    0,   56,    2, 0x08 /* Private */,
       5,    0,   57,    2, 0x08 /* Private */,
       6,    0,   58,    2, 0x08 /* Private */,
       7,    0,   59,    2, 0x08 /* Private */,
       8,    0,   60,    2, 0x08 /* Private */,
       9,    0,   61,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void QT_D::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QT_D *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onMinimizeClicked(); break;
        case 1: _t->onCloseClicked(); break;
        case 2: _t->onThemeToggleClicked(); break;
        case 3: _t->onThemeChanged(); break;
        case 4: _t->onTestInfoClicked(); break;
        case 5: _t->onTestWarningClicked(); break;
        case 6: _t->onTestErrorClicked(); break;
        case 7: _t->onClearLogClicked(); break;
        default: ;
        }
    }
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QT_D::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_QT_D.data,
    qt_meta_data_QT_D,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QT_D::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QT_D::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QT_D.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int QT_D::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
