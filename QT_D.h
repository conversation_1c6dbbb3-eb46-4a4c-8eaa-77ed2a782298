#pragma once

#include <QtWidgets/QMainWindow>
#include <QMouseEvent>
#include <QCloseEvent>
#include <QPoint>
#include <QListWidget>
#include <QStackedWidget>
#include <QHBoxLayout>
#include <QPropertyAnimation>
#include <QPushButton>
#include <QIcon>
#include <QPixmap>
#include <QPainter>
#include <QSvgRenderer>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QComboBox>
#include <QHeaderView>
#include <QAbstractItemView>
#include <memory>
#include "ui_QT_D.h"
#include "Logger.h"
#include "obj.h"

// 前向声明
class SkillDecisionEngine;
class QTextEdit;
class QLineEdit;
struct DecisionResult;
struct MonsterInfo;
struct SkillInfo;

class QT_D : public QMainWindow
{
    Q_OBJECT

public:
    QT_D(QWidget *parent = nullptr);
    ~QT_D();

protected:
    // 鼠标事件处理 - 用于窗口拖拽
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;

    // 窗口关闭事件处理
    void closeEvent(QCloseEvent* event) override;

    // 事件过滤器 - 用于导航栏悬停检测
    bool eventFilter(QObject* obj, QEvent* event) override;

    // Windows消息处理 - 用于热键响应
    bool nativeEvent(const QByteArray& eventType, void* message, long* result) override;

private slots:
    // 窗口控制按钮槽函数
    void onMinimizeClicked();
    void onCloseClicked();

    // 导航栏槽函数
    void onNavigationItemChanged(int currentRow);

    // 日志控制按钮槽函数
    void onClearLogClicked();

    // 热键槽函数
    void onHomeKeyPressed();

    // 技能决策相关槽函数
    void onTestDecisionClicked();
    void onDecisionMade(const DecisionResult& result);
    void onDecisionDebugInfo(const QString& message);

private:
    void initializeWindow();
    void initializeLogger();
    void initializeNavigation();
    void createOtherPages();
    void updateNavigationItems();
    void expandNavigation();
    void collapseNavigation();
    void connectSignals();
    void initializeDmSoft();
    void registerHotKeys();
    void initializeGameSystem();

    // 技能决策相关方法
    void initializeSkillDecisionEngine();
    void createDebugPage();
    void displayDecisionResult(const DecisionResult& result,
                              const QVector<MonsterInfo>& monsters,
                              const QVector<SkillInfo>& skills,
                              const QPointF& playerPos);

    // SVG图标相关方法
    QIcon createSvgIcon(const QString& svgPath, const QColor& color = QColor("#ffffff"), int size = 24);

private:
    Ui::QT_DClass ui;

    // 窗口拖拽相关
    bool m_dragging;
    QPoint m_dragPosition;

    // 日志系统
    std::shared_ptr<Logger> m_logger;

    // 导航栏组件
    QWidget* m_navigationPanel;
    QListWidget* m_navigationList;
    QStackedWidget* m_contentStack;
    QPropertyAnimation* m_navigationAnimation;

    // 页面组件
    QWidget* m_logsPage;
    QWidget* m_settingsPage;
    QWidget* m_debugPage;
    QWidget* m_aboutPage;

    // 抽屉状态
    bool m_navigationExpanded;
    int m_expandedWidth;
    int m_collapsedWidth;

    // 游戏系统
    dmsystem* m_dmSystem;

    // 热键状态
    bool m_homeKeyRegistered;

    // 技能决策系统
    SkillDecisionEngine* m_skillDecisionEngine;

    // 调试页面UI控件
    QTextEdit* m_monsterDataEdit;
    QTextEdit* m_skillDataEdit;
    QLineEdit* m_playerXEdit;
    QLineEdit* m_playerYEdit;
    QPushButton* m_testDecisionBtn;
    QTextEdit* m_decisionResultEdit;
};