#pragma once

#include <QtWidgets/QMainWindow>
#include <QMouseEvent>
#include <QPoint>
#include <memory>
#include "ui_QT_D.h"
#include "Logger.h"

class QT_D : public QMainWindow
{
    Q_OBJECT

public:
    QT_D(QWidget *parent = nullptr);
    ~QT_D();

protected:
    // 鼠标事件处理 - 用于窗口拖拽
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;

private slots:
    // 窗口控制按钮槽函数
    void onMinimizeClicked();
    void onCloseClicked();

    // 日志测试按钮槽函数
    void onTestInfoClicked();
    void onTestWarningClicked();
    void onTestErrorClicked();
    void onClearLogClicked();

private:
    void initializeWindow();
    void initializeLogger();
    void connectSignals();
    void showWelcomeMessage();

private:
    Ui::QT_DClass ui;

    // 窗口拖拽相关
    bool m_dragging;
    QPoint m_dragPosition;

    // 日志系统
    std::shared_ptr<Logger> m_logger;
};

