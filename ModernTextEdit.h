#pragma once

#include <QTextEdit>
#include <QScrollBar>
#include <QWheelEvent>
#include <QPropertyAnimation>
#include <QEasingCurve>
#include <QTimer>

class ModernTextEdit : public QTextEdit
{
    Q_OBJECT

public:
    explicit ModernTextEdit(QWidget* parent = nullptr);
    ~ModernTextEdit();

    // 设置滚动动画参数
    void setScrollAnimationDuration(int duration);
    void setScrollAnimationEasing(QEasingCurve::Type easing);
    void setSmoothScrollEnabled(bool enabled);

protected:
    void wheelEvent(QWheelEvent* event) override;
    void showEvent(QShowEvent* event) override;

private slots:
    void onScrollAnimationFinished();
    void onScrollBarValueChanged(int value);

private:
    void setupScrollBars();
    void applyChromeStyle();
    void smoothScrollTo(int targetValue);

private:
    QPropertyAnimation* m_scrollAnimation;
    QScrollBar* m_verticalScrollBar;
    QScrollBar* m_horizontalScrollBar;
    
    int m_animationDuration;
    QEasingCurve::Type m_easingCurve;
    bool m_smoothScrollEnabled;
    bool m_isAnimating;
    
    int m_targetScrollValue;
    int m_pendingScrollDelta;
};
