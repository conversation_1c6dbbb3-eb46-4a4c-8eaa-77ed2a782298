#include "SideNavigation.h"
#include <QApplication>

SideNavigation::SideNavigation(QWidget* parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_navigationPanel(nullptr)
    , m_navLayout(nullptr)
    , m_stackedWidget(nullptr)
    , m_logsButton(nullptr)
    , m_settingsButton(nullptr)
    , m_debugButton(nullptr)
    , m_aboutButton(nullptr)
    , m_logsPage(nullptr)
    , m_settingsPage(nullptr)
    , m_debugPage(nullptr)
    , m_aboutPage(nullptr)
    , m_logTextEdit(nullptr)
    , m_currentPage(0)
{
    setupUI();
    switchToPage(LogsPage);
}

SideNavigation::~SideNavigation()
{
}

void SideNavigation::setupUI()
{
    // 主布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setSpacing(0);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    
    // 创建导航面板和页面
    createNavigationButtons();
    createPages();
    
    // 设置样式
    m_buttonStyle = 
        "QPushButton {"
        "    background-color: #3c3c3c;"
        "    border: none;"
        "    border-radius: 0px;"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    padding: 15px 10px;"
        "    text-align: center;"
        "    min-height: 50px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #4c4c4c;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #5c5c5c;"
        "}";
    
    m_activeButtonStyle = 
        "QPushButton {"
        "    background-color: #007acc;"
        "    border: none;"
        "    border-radius: 0px;"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 600;"
        "    padding: 15px 10px;"
        "    text-align: center;"
        "    min-height: 50px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #005a9e;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #004578;"
        "}";
    
    updateButtonStyles();
}

void SideNavigation::createNavigationButtons()
{
    // 创建导航面板
    m_navigationPanel = new QWidget();
    m_navigationPanel->setFixedWidth(120);
    m_navigationPanel->setStyleSheet("QWidget { background-color: #2d2d2d; border-right: 1px solid #3c3c3c; }");
    
    m_navLayout = new QVBoxLayout(m_navigationPanel);
    m_navLayout->setSpacing(0);
    m_navLayout->setContentsMargins(0, 0, 0, 0);
    
    // 创建导航按钮
    m_logsButton = new QPushButton("Logs");
    m_settingsButton = new QPushButton("Settings");
    m_debugButton = new QPushButton("Debug");
    m_aboutButton = new QPushButton("About");
    
    // 连接信号
    connect(m_logsButton, &QPushButton::clicked, this, &SideNavigation::onNavigationButtonClicked);
    connect(m_settingsButton, &QPushButton::clicked, this, &SideNavigation::onNavigationButtonClicked);
    connect(m_debugButton, &QPushButton::clicked, this, &SideNavigation::onNavigationButtonClicked);
    connect(m_aboutButton, &QPushButton::clicked, this, &SideNavigation::onNavigationButtonClicked);
    
    // 添加到布局
    m_navLayout->addWidget(m_logsButton);
    m_navLayout->addWidget(m_settingsButton);
    m_navLayout->addWidget(m_debugButton);
    m_navLayout->addWidget(m_aboutButton);
    m_navLayout->addStretch();
    
    m_mainLayout->addWidget(m_navigationPanel);
}

void SideNavigation::createPages()
{
    // 创建堆叠窗口
    m_stackedWidget = new QStackedWidget();
    
    // 创建日志页面
    m_logsPage = new QWidget();
    QVBoxLayout* logsLayout = new QVBoxLayout(m_logsPage);
    logsLayout->setContentsMargins(10, 10, 10, 10);
    
    QLabel* logsTitle = new QLabel("System Logs");
    logsTitle->setStyleSheet("QLabel { color: #ffffff; font-size: 18px; font-weight: bold; margin-bottom: 10px; }");
    logsLayout->addWidget(logsTitle);
    
    // 创建日志文本编辑器
    m_logTextEdit = new QTextEdit();
    m_logTextEdit->setStyleSheet(
        "QTextEdit {"
        "    background-color: #252525;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    color: #ffffff;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: #007acc;"
        "}");
    m_logTextEdit->setReadOnly(true);
    m_logTextEdit->setPlaceholderText("日志输出将显示在这里...");
    logsLayout->addWidget(m_logTextEdit);
    
    // 创建设置页面
    m_settingsPage = new QWidget();
    QVBoxLayout* settingsLayout = new QVBoxLayout(m_settingsPage);
    settingsLayout->setContentsMargins(10, 10, 10, 10);
    
    QLabel* settingsTitle = new QLabel("Settings");
    settingsTitle->setStyleSheet("QLabel { color: #ffffff; font-size: 18px; font-weight: bold; margin-bottom: 10px; }");
    settingsLayout->addWidget(settingsTitle);
    
    QLabel* settingsContent = new QLabel("Settings configuration will be available here.");
    settingsContent->setStyleSheet("QLabel { color: #cccccc; font-size: 14px; }");
    settingsLayout->addWidget(settingsContent);
    settingsLayout->addStretch();
    
    // 创建调试页面
    m_debugPage = new QWidget();
    QVBoxLayout* debugLayout = new QVBoxLayout(m_debugPage);
    debugLayout->setContentsMargins(10, 10, 10, 10);
    
    QLabel* debugTitle = new QLabel("Debug Tools");
    debugTitle->setStyleSheet("QLabel { color: #ffffff; font-size: 18px; font-weight: bold; margin-bottom: 10px; }");
    debugLayout->addWidget(debugTitle);
    
    QLabel* debugContent = new QLabel("Debug tools and utilities will be available here.");
    debugContent->setStyleSheet("QLabel { color: #cccccc; font-size: 14px; }");
    debugLayout->addWidget(debugContent);
    debugLayout->addStretch();
    
    // 创建关于页面
    m_aboutPage = new QWidget();
    QVBoxLayout* aboutLayout = new QVBoxLayout(m_aboutPage);
    aboutLayout->setContentsMargins(10, 10, 10, 10);
    
    QLabel* aboutTitle = new QLabel("About Software");
    aboutTitle->setStyleSheet("QLabel { color: #ffffff; font-size: 18px; font-weight: bold; margin-bottom: 10px; }");
    aboutLayout->addWidget(aboutTitle);
    
    QLabel* aboutContent = new QLabel("QT_D Modern Interface\nVersion 1.0\n\nA modern Qt application with advanced features.");
    aboutContent->setStyleSheet("QLabel { color: #cccccc; font-size: 14px; line-height: 1.5; }");
    aboutLayout->addWidget(aboutContent);
    aboutLayout->addStretch();
    
    // 添加页面到堆叠窗口
    m_stackedWidget->addWidget(m_logsPage);
    m_stackedWidget->addWidget(m_settingsPage);
    m_stackedWidget->addWidget(m_debugPage);
    m_stackedWidget->addWidget(m_aboutPage);
    
    m_mainLayout->addWidget(m_stackedWidget);
}

void SideNavigation::switchToPage(PageIndex page)
{
    m_currentPage = page;
    m_stackedWidget->setCurrentIndex(page);
    updateButtonStyles();
    emit pageChanged(page);
}

void SideNavigation::onNavigationButtonClicked()
{
    QPushButton* sender = qobject_cast<QPushButton*>(QObject::sender());
    if (!sender) return;
    
    if (sender == m_logsButton) {
        switchToPage(LogsPage);
    } else if (sender == m_settingsButton) {
        switchToPage(SettingsPage);
    } else if (sender == m_debugButton) {
        switchToPage(DebugPage);
    } else if (sender == m_aboutButton) {
        switchToPage(AboutPage);
    }
}

void SideNavigation::updateButtonStyles()
{
    // 重置所有按钮样式
    m_logsButton->setStyleSheet(m_buttonStyle);
    m_settingsButton->setStyleSheet(m_buttonStyle);
    m_debugButton->setStyleSheet(m_buttonStyle);
    m_aboutButton->setStyleSheet(m_buttonStyle);
    
    // 设置当前页面按钮的活动样式
    switch (m_currentPage) {
        case LogsPage:
            m_logsButton->setStyleSheet(m_activeButtonStyle);
            break;
        case SettingsPage:
            m_settingsButton->setStyleSheet(m_activeButtonStyle);
            break;
        case DebugPage:
            m_debugButton->setStyleSheet(m_activeButtonStyle);
            break;
        case AboutPage:
            m_aboutButton->setStyleSheet(m_activeButtonStyle);
            break;
    }
}
