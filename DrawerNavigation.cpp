#include "DrawerNavigation.h"
#include <QPainter>
#include <QMouseEvent>
#include <QApplication>

DrawerNavigation::DrawerNavigation(QWidget* parent)
    : QWidget(parent)
    , m_drawerWidget(nullptr)
    , m_overlayWidget(nullptr)
    , m_scroll<PERSON>rea(nullptr)
    , m_contentWidget(nullptr)
    , m_contentLayout(nullptr)
    , m_closeButton(nullptr)
    , m_title<PERSON>abel(nullptr)
    , m_slideAnimation(nullptr)
    , m_overlayAnimation(nullptr)
    , m_isOpen(false)
    , m_drawerWidth(280)
    , m_currentIndex(-1)
{
    setupUI();
    setupAnimations();
    
    // 设置样式
    m_itemStyle = 
        "QPushButton {"
        "    background-color: transparent;"
        "    border: none;"
        "    border-radius: 8px;"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    padding: 12px 20px;"
        "    text-align: left;"
        "    margin: 2px 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.1);"
        "}"
        "QPushButton:pressed {"
        "    background-color: rgba(255, 255, 255, 0.2);"
        "}";
    
    m_selectedItemStyle = 
        "QPushButton {"
        "    background-color: #007acc;"
        "    border: none;"
        "    border-radius: 8px;"
        "    color: #ffffff;"
        "    font-size: 14px;"
        "    font-weight: 600;"
        "    padding: 12px 20px;"
        "    text-align: left;"
        "    margin: 2px 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #005a9e;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #004578;"
        "}";
}

DrawerNavigation::~DrawerNavigation()
{
}

void DrawerNavigation::setupUI()
{
    // 设置主窗口属性
    setFixedSize(parent()->size());
    setAttribute(Qt::WA_TransparentForMouseEvents, false);
    
    // 创建遮罩层
    createOverlay();
    
    // 创建抽屉主体
    m_drawerWidget = new QWidget(this);
    m_drawerWidget->setFixedSize(m_drawerWidth, height());
    m_drawerWidget->move(-m_drawerWidth, 0);
    m_drawerWidget->setStyleSheet(
        "QWidget {"
        "    background-color: #2d2d2d;"
        "    border-right: 1px solid #3c3c3c;"
        "}");
    
    // 添加阴影效果
    QGraphicsDropShadowEffect* shadowEffect = new QGraphicsDropShadowEffect();
    shadowEffect->setBlurRadius(20);
    shadowEffect->setColor(QColor(0, 0, 0, 100));
    shadowEffect->setOffset(2, 0);
    m_drawerWidget->setGraphicsEffect(shadowEffect);
    
    // 创建滚动区域
    m_scrollArea = new QScrollArea(m_drawerWidget);
    m_scrollArea->setGeometry(0, 0, m_drawerWidth, height());
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setStyleSheet(
        "QScrollArea {"
        "    border: none;"
        "    background-color: transparent;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 8px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background-color: #555555;"
        "    border-radius: 4px;"
        "    min-height: 20px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: #666666;"
        "}"
        "QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}");
    
    // 创建内容区域
    m_contentWidget = new QWidget();
    m_contentLayout = new QVBoxLayout(m_contentWidget);
    m_contentLayout->setSpacing(5);
    m_contentLayout->setContentsMargins(0, 20, 0, 20);
    
    // 创建标题
    m_titleLabel = new QLabel("Navigation", m_contentWidget);
    m_titleLabel->setStyleSheet(
        "QLabel {"
        "    color: #ffffff;"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "    padding: 15px 20px 10px 20px;"
        "    background-color: transparent;"
        "}");
    m_contentLayout->addWidget(m_titleLabel);
    
    // 创建关闭按钮
    m_closeButton = new QPushButton("×", m_contentWidget);
    m_closeButton->setFixedSize(30, 30);
    m_closeButton->move(m_drawerWidth - 40, 10);
    m_closeButton->setStyleSheet(
        "QPushButton {"
        "    background-color: transparent;"
        "    border: none;"
        "    border-radius: 15px;"
        "    color: #ffffff;"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.1);"
        "}"
        "QPushButton:pressed {"
        "    background-color: rgba(255, 255, 255, 0.2);"
        "}");
    
    connect(m_closeButton, &QPushButton::clicked, this, &DrawerNavigation::closeDrawer);
    
    // 添加弹性空间
    m_contentLayout->addStretch();
    
    // 设置滚动区域内容
    m_scrollArea->setWidget(m_contentWidget);
}

void DrawerNavigation::setupAnimations()
{
    // 滑动动画
    m_slideAnimation = new QPropertyAnimation(m_drawerWidget, "pos", this);
    m_slideAnimation->setDuration(300);
    m_slideAnimation->setEasingCurve(QEasingCurve::OutQuart);
    
    // 遮罩动画
    m_overlayAnimation = new QPropertyAnimation(m_overlayWidget, "windowOpacity", this);
    m_overlayAnimation->setDuration(300);
    m_overlayAnimation->setEasingCurve(QEasingCurve::OutQuart);
    
    connect(m_slideAnimation, &QPropertyAnimation::finished,
            this, &DrawerNavigation::onAnimationFinished);
}

void DrawerNavigation::createOverlay()
{
    m_overlayWidget = new QWidget(this);
    m_overlayWidget->setGeometry(0, 0, width(), height());
    m_overlayWidget->setStyleSheet("background-color: rgba(0, 0, 0, 0.5);");
    m_overlayWidget->hide();
    m_overlayWidget->installEventFilter(this);
}

void DrawerNavigation::openDrawer()
{
    if (m_isOpen) return;

    m_isOpen = true;
    show();
    m_overlayWidget->show();

    // 动画到打开位置
    m_slideAnimation->setStartValue(QPoint(-m_drawerWidth, 0));
    m_slideAnimation->setEndValue(QPoint(0, 0));
    m_slideAnimation->start();

    // 遮罩淡入
    m_overlayAnimation->setStartValue(0.0);
    m_overlayAnimation->setEndValue(1.0);
    m_overlayAnimation->start();
}

void DrawerNavigation::closeDrawer()
{
    if (!m_isOpen) return;

    m_isOpen = false;

    // 动画到关闭位置
    m_slideAnimation->setStartValue(QPoint(0, 0));
    m_slideAnimation->setEndValue(QPoint(-m_drawerWidth, 0));
    m_slideAnimation->start();

    // 遮罩淡出
    m_overlayAnimation->setStartValue(1.0);
    m_overlayAnimation->setEndValue(0.0);
    m_overlayAnimation->start();
}

void DrawerNavigation::toggleDrawer()
{
    if (m_isOpen) {
        closeDrawer();
    } else {
        openDrawer();
    }
}

void DrawerNavigation::setDrawerWidth(int width)
{
    m_drawerWidth = width;
    if (m_drawerWidget) {
        m_drawerWidget->setFixedWidth(width);
        m_scrollArea->setFixedWidth(width);
        m_closeButton->move(width - 40, 10);
        updateDrawerPosition();
    }
}

void DrawerNavigation::addNavigationItem(const QString& text, const QString& icon, QWidget* targetWidget)
{
    QPushButton* item = new QPushButton(text, m_contentWidget);
    item->setStyleSheet(m_itemStyle);
    item->setMinimumHeight(45);

    // 如果有图标，设置图标
    if (!icon.isEmpty()) {
        item->setText(QString("%1  %2").arg(icon, text));
    }

    // 连接点击信号
    connect(item, &QPushButton::clicked, this, &DrawerNavigation::onNavigationItemClicked);

    // 添加到布局（在弹性空间之前）
    m_contentLayout->insertWidget(m_contentLayout->count() - 1, item);

    // 保存引用
    m_navigationItems.append(item);
    m_targetWidgets.append(targetWidget);
}

void DrawerNavigation::addSeparator()
{
    QFrame* separator = new QFrame(m_contentWidget);
    separator->setFrameShape(QFrame::HLine);
    separator->setFrameShadow(QFrame::Sunken);
    separator->setStyleSheet(
        "QFrame {"
        "    color: #3c3c3c;"
        "    margin: 10px 20px;"
        "}");

    m_contentLayout->insertWidget(m_contentLayout->count() - 1, separator);
}

void DrawerNavigation::setCurrentItem(int index)
{
    if (index < 0 || index >= m_navigationItems.size()) return;

    // 重置所有项的样式
    for (QPushButton* item : m_navigationItems) {
        item->setStyleSheet(m_itemStyle);
    }

    // 设置当前项的样式
    if (index >= 0 && index < m_navigationItems.size()) {
        m_navigationItems[index]->setStyleSheet(m_selectedItemStyle);
        m_currentIndex = index;
    }
}

void DrawerNavigation::onNavigationItemClicked()
{
    QPushButton* sender = qobject_cast<QPushButton*>(QObject::sender());
    if (!sender) return;

    int index = m_navigationItems.indexOf(sender);
    if (index >= 0) {
        setCurrentItem(index);
        emit navigationItemClicked(index, sender->text());
        closeDrawer();
    }
}

void DrawerNavigation::onAnimationFinished()
{
    if (!m_isOpen) {
        m_overlayWidget->hide();
        hide();
        emit drawerClosed();
    } else {
        emit drawerOpened();
    }
}

void DrawerNavigation::updateDrawerPosition()
{
    if (!m_isOpen && m_drawerWidget) {
        m_drawerWidget->move(-m_drawerWidth, 0);
    }
}

void DrawerNavigation::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event);
    // 透明背景，不需要绘制
}

bool DrawerNavigation::eventFilter(QObject* obj, QEvent* event)
{
    if (obj == m_overlayWidget && event->type() == QEvent::MouseButtonPress) {
        closeDrawer();
        return true;
    }
    return QWidget::eventFilter(obj, event);
}
