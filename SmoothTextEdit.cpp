#include "SmoothTextEdit.h"
#include <QWheelEvent>

SmoothTextEdit::SmoothTextEdit(QWidget* parent)
    : QTextEdit(parent)
    , m_verticalScrollBar(nullptr)
    , m_horizontalScrollBar(nullptr)
{
    setupSmoothScrollBars();
    applyLogTextStyle();
}

SmoothTextEdit::~SmoothTextEdit()
{
    // 滚动条会被Qt自动管理，不需要手动删除
}

void SmoothTextEdit::setupSmoothScrollBars()
{
    // 创建平滑垂直滚动条
    m_verticalScrollBar = new SmoothScrollBar(Qt::Vertical, this);
    m_verticalScrollBar->setAnimationDuration(300);
    m_verticalScrollBar->setEasingCurve(QEasingCurve::OutQuint);
    setVerticalScrollBar(m_verticalScrollBar);
    
    // 创建平滑水平滚动条
    m_horizontalScrollBar = new SmoothScrollBar(Qt::Horizontal, this);
    m_horizontalScrollBar->setAnimationDuration(300);
    m_horizontalScrollBar->setEasingCurve(QEasingCurve::OutQuint);
    setHorizontalScrollBar(m_horizontalScrollBar);
}

void SmoothTextEdit::applyLogTextStyle()
{
    QString style = 
        "QTextEdit {"
        "    background-color: #252525;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    color: #ffffff;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: #007acc;"
        "}";
    
    setStyleSheet(style);
}

void SmoothTextEdit::setScrollAnimationDuration(int duration)
{
    if (m_verticalScrollBar) {
        m_verticalScrollBar->setAnimationDuration(duration);
    }
    if (m_horizontalScrollBar) {
        m_horizontalScrollBar->setAnimationDuration(duration);
    }
}

void SmoothTextEdit::setScrollEasingCurve(QEasingCurve::Type curve)
{
    if (m_verticalScrollBar) {
        m_verticalScrollBar->setEasingCurve(curve);
    }
    if (m_horizontalScrollBar) {
        m_horizontalScrollBar->setEasingCurve(curve);
    }
}

void SmoothTextEdit::wheelEvent(QWheelEvent* event)
{
    // 计算滚动距离并使用平滑滚动
    if (m_verticalScrollBar) {
        int delta = -event->angleDelta().y() / 8;
        m_verticalScrollBar->smoothScrollBy(delta);
        event->accept();
    } else {
        QTextEdit::wheelEvent(event);
    }
}
