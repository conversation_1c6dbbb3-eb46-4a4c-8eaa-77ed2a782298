#pragma once

#include <Windows.h>
#include "offset.h"
#include "obj.h"

class GameData
{
public:
    GameData(dmsystem* dmObj);
    ~GameData();

    // 获取人物指针
    __int64 GetCharacterPointer();

    // 获取人物动作
    int GetCharacterAction();

    // 获取人物坐标
    void GetCharacterPosition(float& x, float& y, float& z);

private:
    dmsystem* obj;  // dmsystem对象指针

    // 偏移量
    static const __int64 POINTER_OFFSET = 0x30;
};
