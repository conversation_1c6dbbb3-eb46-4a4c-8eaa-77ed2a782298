#include "ModernTextEdit.h"
#include "ThemeManager.h"
#include <QApplication>
#include <QScrollBar>

ModernTextEdit::ModernTextEdit(QWidget* parent)
    : QTextEdit(parent)
    , m_scrollAnimation(nullptr)
    , m_verticalScrollBar(nullptr)
    , m_horizontalScrollBar(nullptr)
    , m_animationDuration(200)
    , m_easingCurve(QEasingCurve::OutCubic)
    , m_smoothScrollEnabled(true)
    , m_isAnimating(false)
    , m_targetScrollValue(0)
    , m_pendingScrollDelta(0)
{
    setupScrollBars();
    applyThemeStyle();

    // 连接主题变化信号
    connect(ThemeManager::instance(), &ThemeManager::themeChanged,
            this, &ModernTextEdit::onThemeChanged);
}

ModernTextEdit::~ModernTextEdit()
{
    if (m_scrollAnimation) {
        m_scrollAnimation->stop();
        delete m_scrollAnimation;
    }
}

void ModernTextEdit::setupScrollBars()
{
    m_verticalScrollBar = verticalScrollBar();
    m_horizontalScrollBar = horizontalScrollBar();

    m_scrollAnimation = new QPropertyAnimation(m_verticalScrollBar, "value", this);
    m_scrollAnimation->setEasingCurve(m_easingCurve);
    m_scrollAnimation->setDuration(m_animationDuration);

    connect(m_scrollAnimation, &QPropertyAnimation::finished,
            this, &ModernTextEdit::onScrollAnimationFinished);

    connect(m_verticalScrollBar, &QScrollBar::valueChanged,
            this, &ModernTextEdit::onScrollBarValueChanged);
}

void ModernTextEdit::applyChromeStyle()
{
    // 保留原有方法以兼容性，但现在使用主题样式
    applyThemeStyle();
}

void ModernTextEdit::applyThemeStyle()
{
    setStyleSheet(ThemeManager::instance()->getLogTextEditStyle());
}

void ModernTextEdit::setScrollAnimationDuration(int duration)
{
    m_animationDuration = duration;
    if (m_scrollAnimation) {
        m_scrollAnimation->setDuration(duration);
    }
}

void ModernTextEdit::setScrollAnimationEasing(QEasingCurve::Type easing)
{
    m_easingCurve = easing;
    if (m_scrollAnimation) {
        m_scrollAnimation->setEasingCurve(easing);
    }
}

void ModernTextEdit::setSmoothScrollEnabled(bool enabled)
{
    m_smoothScrollEnabled = enabled;
}

void ModernTextEdit::wheelEvent(QWheelEvent* event)
{
    if (!m_smoothScrollEnabled || !m_verticalScrollBar) {
        QTextEdit::wheelEvent(event);
        return;
    }

    int numDegrees = event->angleDelta().y() / 8;
    int numSteps = numDegrees / 15;
    int delta = -numSteps * 40;

    if (delta == 0) {
        event->accept();
        return;
    }

    if (m_isAnimating) {
        m_scrollAnimation->stop();
        m_isAnimating = false;
    }

    smoothScrollTo(m_verticalScrollBar->value() + delta);
    event->accept();
}

void ModernTextEdit::smoothScrollTo(int targetValue)
{
    if (!m_verticalScrollBar || !m_smoothScrollEnabled) {
        return;
    }

    targetValue = qBound(m_verticalScrollBar->minimum(),
                        targetValue,
                        m_verticalScrollBar->maximum());

    m_targetScrollValue = targetValue;

    if (targetValue == m_verticalScrollBar->value()) {
        return;
    }

    if (m_scrollAnimation->state() == QPropertyAnimation::Running) {
        m_scrollAnimation->stop();
    }

    m_scrollAnimation->setStartValue(m_verticalScrollBar->value());
    m_scrollAnimation->setEndValue(targetValue);

    m_isAnimating = true;
    m_scrollAnimation->start();
}

void ModernTextEdit::onScrollAnimationFinished()
{
    m_isAnimating = false;
    m_pendingScrollDelta = 0;
}

void ModernTextEdit::onScrollBarValueChanged(int value)
{
    if (!m_isAnimating) {
        m_targetScrollValue = value;
    }
}

void ModernTextEdit::onThemeChanged()
{
    applyThemeStyle();
}

void ModernTextEdit::showEvent(QShowEvent* event)
{
    QTextEdit::showEvent(event);
    applyThemeStyle();
}