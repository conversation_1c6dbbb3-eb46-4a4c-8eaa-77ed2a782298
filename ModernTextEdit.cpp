#include "ModernTextEdit.h"
#include <QApplication>
#include <QScrollBar>

ModernTextEdit::ModernTextEdit(QWidget* parent)
    : QTextEdit(parent)
    , m_scrollAnimation(nullptr)
    , m_verticalScrollBar(nullptr)
    , m_horizontalScrollBar(nullptr)
    , m_animationDuration(200)
    , m_easingCurve(QEasingCurve::OutCubic)
    , m_smoothScrollEnabled(true)
    , m_isAnimating(false)
    , m_targetScrollValue(0)
    , m_pendingScrollDelta(0)
{
    setupScrollBars();
    applyChromeStyle();
}

ModernTextEdit::~ModernTextEdit()
{
    if (m_scrollAnimation) {
        m_scrollAnimation->stop();
        delete m_scrollAnimation;
    }
}

void ModernTextEdit::setupScrollBars()
{
    m_verticalScrollBar = verticalScrollBar();
    m_horizontalScrollBar = horizontalScrollBar();

    m_scrollAnimation = new QPropertyAnimation(m_verticalScrollBar, "value", this);
    m_scrollAnimation->setEasingCurve(m_easingCurve);
    m_scrollAnimation->setDuration(m_animationDuration);

    connect(m_scrollAnimation, &QPropertyAnimation::finished,
            this, &ModernTextEdit::onScrollAnimationFinished);

    connect(m_verticalScrollBar, &QScrollBar::valueChanged,
            this, &ModernTextEdit::onScrollBarValueChanged);
}

void ModernTextEdit::applyChromeStyle()
{
    QString style =
        "QTextEdit {"
        "    background-color: #252525;"
        "    border: 1px solid #3c3c3c;"
        "    border-radius: 6px;"
        "    color: #ffffff;"
        "    font-family: 'Consolas', 'Monaco', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: #007acc;"
        "}"
        "QScrollBar:vertical {"
        "    background-color: transparent;"
        "    width: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #6a6a6a, stop:0.5 #7a7a7a, stop:1 #6a6a6a);"
        "    border: 1px solid #4a4a4a;"
        "    border-radius: 6px;"
        "    min-height: 30px;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:vertical {"
        "    height: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: transparent;"
        "    height: 12px;"
        "    border: none;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6a6a6a, stop:0.5 #7a7a7a, stop:1 #6a6a6a);"
        "    border: 1px solid #4a4a4a;"
        "    border-radius: 6px;"
        "    min-width: 30px;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #7f7f7f;"
        "}"
        "QScrollBar::handle:horizontal:pressed {"
        "    background-color: #999999;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "    width: 0px;"
        "    border: none;"
        "    background: none;"
        "}"
        "QScrollBar::add-page:horizontal {"
        "    background: none;"
        "}"
        "QScrollBar::sub-page:horizontal {"
        "    background: none;"
        "}";

    setStyleSheet(style);
}

void ModernTextEdit::setScrollAnimationDuration(int duration)
{
    m_animationDuration = duration;
    if (m_scrollAnimation) {
        m_scrollAnimation->setDuration(duration);
    }
}

void ModernTextEdit::setScrollAnimationEasing(QEasingCurve::Type easing)
{
    m_easingCurve = easing;
    if (m_scrollAnimation) {
        m_scrollAnimation->setEasingCurve(easing);
    }
}

void ModernTextEdit::setSmoothScrollEnabled(bool enabled)
{
    m_smoothScrollEnabled = enabled;
}

void ModernTextEdit::wheelEvent(QWheelEvent* event)
{
    if (!m_smoothScrollEnabled || !m_verticalScrollBar) {
        QTextEdit::wheelEvent(event);
        return;
    }

    int numDegrees = event->angleDelta().y() / 8;
    int numSteps = numDegrees / 15;
    int delta = -numSteps * 40;

    if (delta == 0) {
        event->accept();
        return;
    }

    if (m_isAnimating) {
        m_scrollAnimation->stop();
        m_isAnimating = false;
    }

    smoothScrollTo(m_verticalScrollBar->value() + delta);
    event->accept();
}

void ModernTextEdit::smoothScrollTo(int targetValue)
{
    if (!m_verticalScrollBar || !m_smoothScrollEnabled) {
        return;
    }

    targetValue = qBound(m_verticalScrollBar->minimum(),
                        targetValue,
                        m_verticalScrollBar->maximum());

    m_targetScrollValue = targetValue;

    if (targetValue == m_verticalScrollBar->value()) {
        return;
    }

    if (m_scrollAnimation->state() == QPropertyAnimation::Running) {
        m_scrollAnimation->stop();
    }

    m_scrollAnimation->setStartValue(m_verticalScrollBar->value());
    m_scrollAnimation->setEndValue(targetValue);

    m_isAnimating = true;
    m_scrollAnimation->start();
}

void ModernTextEdit::onScrollAnimationFinished()
{
    m_isAnimating = false;
    m_pendingScrollDelta = 0;
}

void ModernTextEdit::onScrollBarValueChanged(int value)
{
    if (!m_isAnimating) {
        m_targetScrollValue = value;
    }
}

void ModernTextEdit::showEvent(QShowEvent* event)
{
    QTextEdit::showEvent(event);
    applyChromeStyle();
}