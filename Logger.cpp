#include "Logger.h"
#include <QScrollBar>
#include <QApplication>

std::shared_ptr<Logger> Logger::m_instance = nullptr;
QMutex Logger::m_instanceMutex;

const QString Logger::INFO_COLOR = "#3498db";
const QString Logger::WARNING_COLOR = "#f39c12";
const QString Logger::ERROR_COLOR = "#e74c3c";
const QString Logger::SUCCESS_COLOR = "#27ae60";
const QString Logger::DEBUG_COLOR = "#95a5a6";
const QString Logger::TIMESTAMP_COLOR = "#7f8c8d";
const QString Logger::MODULE_COLOR = "#9b59b6";

std::shared_ptr<Logger> Logger::getInstance()
{
    QMutexLocker locker(&m_instanceMutex);
    if (!m_instance) {
        struct MakeSharedEnabler : public Logger {
            MakeSharedEnabler() : Logger() {}
        };
        m_instance = std::make_shared<MakeSharedEnabler>();
    }
    return m_instance;
}

Logger::Logger(QObject* parent)
    : QObject(parent)
    , m_logWidget(nullptr)
    , m_timestampEnabled(true)
    , m_moduleEnabled(true)
    , m_maxLines(1000)
    , m_currentLines(0)
{
}

void Logger::setLogWidget(QTextEdit* textEdit)
{
    QMutexLocker locker(&m_logMutex);
    m_logWidget = textEdit;

    if (m_logWidget) {
        // 设置Chrome风格的滚动条样式
        QString scrollBarStyle =
            "QTextEdit {"
            "    background-color: #252525;"
            "    border: 1px solid #3c3c3c;"
            "    border-radius: 6px;"
            "    color: #ffffff;"
            "    font-family: 'Consolas', 'Monaco', monospace;"
            "    font-size: 12px;"
            "    padding: 10px;"
            "    selection-background-color: #007acc;"
            "}"
            "QScrollBar:vertical {"
            "    background-color: transparent;"
            "    width: 12px;"
            "    border: none;"
            "    margin: 0px;"
            "}"
            "QScrollBar::handle:vertical {"
            "    background-color: #5f5f5f;"
            "    border: none;"
            "    border-radius: 6px;"
            "    min-height: 30px;"
            "    margin: 0px 2px;"
            "}"
            "QScrollBar::handle:vertical:hover {"
            "    background-color: #7f7f7f;"
            "}"
            "QScrollBar::handle:vertical:pressed {"
            "    background-color: #999999;"
            "}"
            "QScrollBar::add-line:vertical {"
            "    height: 0px;"
            "    border: none;"
            "    background: none;"
            "}"
            "QScrollBar::sub-line:vertical {"
            "    height: 0px;"
            "    border: none;"
            "    background: none;"
            "}"
            "QScrollBar::add-page:vertical {"
            "    background: none;"
            "}"
            "QScrollBar::sub-page:vertical {"
            "    background: none;"
            "}"
            "QScrollBar:horizontal {"
            "    background-color: transparent;"
            "    height: 12px;"
            "    border: none;"
            "    margin: 0px;"
            "}"
            "QScrollBar::handle:horizontal {"
            "    background-color: #5f5f5f;"
            "    border: none;"
            "    border-radius: 6px;"
            "    min-width: 30px;"
            "    margin: 2px 0px;"
            "}"
            "QScrollBar::handle:horizontal:hover {"
            "    background-color: #7f7f7f;"
            "}"
            "QScrollBar::handle:horizontal:pressed {"
            "    background-color: #999999;"
            "}"
            "QScrollBar::add-line:horizontal {"
            "    width: 0px;"
            "    border: none;"
            "    background: none;"
            "}"
            "QScrollBar::sub-line:horizontal {"
            "    width: 0px;"
            "    border: none;"
            "    background: none;"
            "}"
            "QScrollBar::add-page:horizontal {"
            "    background: none;"
            "}"
            "QScrollBar::sub-page:horizontal {"
            "    background: none;"
            "}";

        m_logWidget->setStyleSheet(scrollBarStyle);
    }
}



void Logger::info(const QString& message, const QString& module)
{
    log(LogLevel::Info, message, module);
}

void Logger::warning(const QString& message, const QString& module)
{
    log(LogLevel::Warning, message, module);
}

void Logger::error(const QString& message, const QString& module)
{
    log(LogLevel::Error, message, module);
}

void Logger::success(const QString& message, const QString& module)
{
    log(LogLevel::Success, message, module);
}

void Logger::debug(const QString& message, const QString& module)
{
    log(LogLevel::Debug, message, module);
}

void Logger::clear()
{
    QMutexLocker locker(&m_logMutex);
    if (m_logWidget) {
        m_logWidget->clear();
        m_currentLines = 0;
    }
}

void Logger::setTimestampEnabled(bool enabled)
{
    m_timestampEnabled = enabled;
}

void Logger::setModuleEnabled(bool enabled)
{
    m_moduleEnabled = enabled;
}

void Logger::setMaxLines(int maxLines)
{
    m_maxLines = maxLines;
}

void Logger::log(LogLevel level, const QString& message, const QString& module)
{
    QMutexLocker locker(&m_logMutex);
    
    if (!m_logWidget) {
        return;
    }

    QString logHtml;
    
    if (m_timestampEnabled) {
        logHtml += QString("<span style='color: %1; font-weight: normal;'>[%2]</span> ")
                   .arg(TIMESTAMP_COLOR)
                   .arg(formatTimestamp());
    }
    
    logHtml += QString("<span style='color: %1; font-weight: bold;'>%2 %3</span> ")
               .arg(getLevelColor(level))
               .arg(getLevelIcon(level))
               .arg(getLevelName(level));
    
    if (m_moduleEnabled && !module.isEmpty()) {
        logHtml += QString("<span style='color: %1; font-weight: normal;'>[%2]</span> ")
                   .arg(MODULE_COLOR)
                   .arg(module);
    }
    
    logHtml += QString("<span style='color: #ffffff;'>%1</span>")
               .arg(message.toHtmlEscaped());

    m_logWidget->append(logHtml);
    m_currentLines++;
    
    QScrollBar* scrollBar = m_logWidget->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
    
    checkAndCleanupLogs();
    
    QApplication::processEvents();
}

QString Logger::getLevelColor(LogLevel level) const
{
    switch (level) {
        case LogLevel::Info:    return INFO_COLOR;
        case LogLevel::Warning: return WARNING_COLOR;
        case LogLevel::Error:   return ERROR_COLOR;
        case LogLevel::Success: return SUCCESS_COLOR;
        case LogLevel::Debug:   return DEBUG_COLOR;
        default:                return INFO_COLOR;
    }
}

QString Logger::getLevelIcon(LogLevel level) const
{
    switch (level) {
        case LogLevel::Info:    return "[I]";
        case LogLevel::Warning: return "[W]";
        case LogLevel::Error:   return "[E]";
        case LogLevel::Success: return "[S]";
        case LogLevel::Debug:   return "[D]";
        default:                return "[I]";
    }
}

QString Logger::getLevelName(LogLevel level) const
{
    switch (level) {
        case LogLevel::Info:    return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error:   return "ERROR";
        case LogLevel::Success: return "SUCCESS";
        case LogLevel::Debug:   return "DEBUG";
        default:                return "INFO";
    }
}

QString Logger::formatTimestamp() const
{
    return QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
}

void Logger::checkAndCleanupLogs()
{
    if (m_maxLines > 0 && m_currentLines > m_maxLines) {
        QString currentText = m_logWidget->toPlainText();
        QStringList lines = currentText.split('\n');
        
        if (lines.size() > m_maxLines) {
            QStringList newLines = lines.mid(lines.size() - m_maxLines);
            m_logWidget->clear();
            m_logWidget->setPlainText(newLines.join('\n'));
            m_currentLines = m_maxLines;
        }
    }
}
