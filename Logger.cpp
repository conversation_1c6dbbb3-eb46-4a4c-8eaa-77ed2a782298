#include "Logger.h"
#include <QScrollBar>
#include <QApplication>

// 静态成员初始化
std::shared_ptr<Logger> Logger::m_instance = nullptr;
QMutex Logger::m_instanceMutex;

// 现代化配色方案 - 2025年流行色彩
const QString Logger::INFO_COLOR = "#3498db";        // 现代蓝
const QString Logger::WARNING_COLOR = "#f39c12";     // 活力橙
const QString Logger::ERROR_COLOR = "#e74c3c";       // 警示红
const QString Logger::SUCCESS_COLOR = "#27ae60";     // 成功绿
const QString Logger::DEBUG_COLOR = "#95a5a6";       // 中性灰
const QString Logger::TIMESTAMP_COLOR = "#7f8c8d";   // 时间戳灰
const QString Logger::MODULE_COLOR = "#9b59b6";      // 模块紫

std::shared_ptr<Logger> Logger::getInstance()
{
    QMutexLocker locker(&m_instanceMutex);
    if (!m_instance) {
        m_instance = std::shared_ptr<Logger>(new Logger());
    }
    return m_instance;
}

Logger::Logger(QObject* parent)
    : QObject(parent)
    , m_logWidget(nullptr)
    , m_timestampEnabled(true)
    , m_moduleEnabled(true)
    , m_maxLines(1000)
    , m_currentLines(0)
{
}

void Logger::setLogWidget(QTextEdit* textEdit)
{
    QMutexLocker locker(&m_logMutex);
    m_logWidget = textEdit;
    
    if (m_logWidget) {
        // 设置现代化的样式
        m_logWidget->setStyleSheet(
            "QTextEdit {"
            "    background-color: #252525;"
            "    border: 1px solid #3c3c3c;"
            "    border-radius: 6px;"
            "    color: #ffffff;"
            "    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;"
            "    font-size: 12px;"
            "    padding: 10px;"
            "    selection-background-color: #007acc;"
            "}"
            "QScrollBar:vertical {"
            "    background-color: #2d2d2d;"
            "    width: 12px;"
            "    border-radius: 6px;"
            "}"
            "QScrollBar::handle:vertical {"
            "    background-color: #555555;"
            "    border-radius: 6px;"
            "    min-height: 20px;"
            "}"
            "QScrollBar::handle:vertical:hover {"
            "    background-color: #666666;"
            "}"
            "QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {"
            "    border: none;"
            "    background: none;"
            "}"
        );
    }
}

void Logger::info(const QString& message, const QString& module)
{
    log(LogLevel::Info, message, module);
}

void Logger::warning(const QString& message, const QString& module)
{
    log(LogLevel::Warning, message, module);
}

void Logger::error(const QString& message, const QString& module)
{
    log(LogLevel::Error, message, module);
}

void Logger::success(const QString& message, const QString& module)
{
    log(LogLevel::Success, message, module);
}

void Logger::debug(const QString& message, const QString& module)
{
    log(LogLevel::Debug, message, module);
}

void Logger::clear()
{
    QMutexLocker locker(&m_logMutex);
    if (m_logWidget) {
        m_logWidget->clear();
        m_currentLines = 0;
    }
}

void Logger::setTimestampEnabled(bool enabled)
{
    m_timestampEnabled = enabled;
}

void Logger::setModuleEnabled(bool enabled)
{
    m_moduleEnabled = enabled;
}

void Logger::setMaxLines(int maxLines)
{
    m_maxLines = maxLines;
}

void Logger::log(LogLevel level, const QString& message, const QString& module)
{
    QMutexLocker locker(&m_logMutex);
    
    if (!m_logWidget) {
        return;
    }

    // 构建日志HTML格式
    QString logHtml;
    
    // 时间戳
    if (m_timestampEnabled) {
        logHtml += QString("<span style='color: %1; font-weight: normal;'>[%2]</span> ")
                   .arg(TIMESTAMP_COLOR)
                   .arg(formatTimestamp());
    }
    
    // 日志级别图标和名称
    logHtml += QString("<span style='color: %1; font-weight: bold;'>%2 %3</span> ")
               .arg(getLevelColor(level))
               .arg(getLevelIcon(level))
               .arg(getLevelName(level));
    
    // 模块名称
    if (m_moduleEnabled && !module.isEmpty()) {
        logHtml += QString("<span style='color: %1; font-weight: normal;'>[%2]</span> ")
                   .arg(MODULE_COLOR)
                   .arg(module);
    }
    
    // 消息内容
    logHtml += QString("<span style='color: #ffffff;'>%1</span>")
               .arg(message.toHtmlEscaped());

    // 添加到日志控件
    m_logWidget->append(logHtml);
    m_currentLines++;
    
    // 自动滚动到底部
    QScrollBar* scrollBar = m_logWidget->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
    
    // 检查是否需要清理旧日志
    checkAndCleanupLogs();
    
    // 强制刷新界面
    QApplication::processEvents();
}

QString Logger::getLevelColor(LogLevel level) const
{
    switch (level) {
        case LogLevel::Info:    return INFO_COLOR;
        case LogLevel::Warning: return WARNING_COLOR;
        case LogLevel::Error:   return ERROR_COLOR;
        case LogLevel::Success: return SUCCESS_COLOR;
        case LogLevel::Debug:   return DEBUG_COLOR;
        default:                return INFO_COLOR;
    }
}

QString Logger::getLevelIcon(LogLevel level) const
{
    switch (level) {
        case LogLevel::Info:    return "ℹ️";
        case LogLevel::Warning: return "⚠️";
        case LogLevel::Error:   return "❌";
        case LogLevel::Success: return "✅";
        case LogLevel::Debug:   return "🔧";
        default:                return "ℹ️";
    }
}

QString Logger::getLevelName(LogLevel level) const
{
    switch (level) {
        case LogLevel::Info:    return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error:   return "ERROR";
        case LogLevel::Success: return "SUCCESS";
        case LogLevel::Debug:   return "DEBUG";
        default:                return "INFO";
    }
}

QString Logger::formatTimestamp() const
{
    return QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
}

void Logger::checkAndCleanupLogs()
{
    if (m_maxLines > 0 && m_currentLines > m_maxLines) {
        // 清理前面的日志行，保留最新的日志
        QString currentText = m_logWidget->toPlainText();
        QStringList lines = currentText.split('\n');
        
        if (lines.size() > m_maxLines) {
            // 保留最新的行
            QStringList newLines = lines.mid(lines.size() - m_maxLines);
            m_logWidget->clear();
            m_logWidget->setPlainText(newLines.join('\n'));
            m_currentLines = m_maxLines;
        }
    }
}
