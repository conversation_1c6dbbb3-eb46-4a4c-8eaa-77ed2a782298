#include "SkillDecisionEngine.h"
#include <QCoreApplication>
#include <QDebug>

void testBasicFunctionality()
{
    qDebug() << "=== 基础功能测试 ===";

    // 测试距离计算
    QPointF p1(0, 0);
    QPointF p2(3, 4);
    float distance = SkillDecisionEngine::calculateDistance(p1, p2);
    qDebug() << "距离计算测试: (0,0) 到 (3,4) = " << distance << " (期望: 5.0)";

    // 创建技能决策引擎
    SkillDecisionEngine engine;
    
    // 创建测试数据
    QVector<MonsterInfo> monsters;
    
    // 添加一些测试怪物
    MonsterInfo monster1(100, 200, "哥布林", 0);
    MonsterInfo monster2(150, 180, "精英哥布林", 1);
    MonsterInfo monster3(200, 220, "普通怪", 0);
    MonsterInfo monster4(180, 200, "骷髅兵", 0);
    
    monsters.append(monster1);
    monsters.append(monster2);
    monsters.append(monster3);
    monsters.append(monster4);
    
    // 创建技能数据
    QVector<SkillInfo> skills;
    skills.append(SkillInfo("拔刀斩", "Q", true));
    skills.append(SkillInfo("普通攻击", "X", true));
    skills.append(SkillInfo("鬼斩", "W", true));
    skills.append(SkillInfo("上挑", "E", false)); // 冷却中
    
    // 角色位置
    QPointF playerPos(50, 50);
    
    // 执行决策
    qDebug() << "开始技能决策测试...";
    qDebug() << "角色位置:" << playerPos;
    qDebug() << "怪物数量:" << monsters.size();
    qDebug() << "可用技能数量:" << skills.size();
    
    DecisionResult result = engine.makeDecision(monsters, skills, playerPos);
    
    // 输出结果
    qDebug() << "\n=== 决策结果 ===";
    qDebug() << "推荐技能:" << result.skillName << "(" << result.skillKey << ")";
    qDebug() << "目标位置:" << result.targetPos;
    qDebug() << "预期命中:" << result.expectedHits << "个目标";
    qDebug() << "效率评分:" << result.efficiency;
    qDebug() << "策略说明:" << result.strategy;
    qDebug() << "决策原因:" << result.reason;
    
    // 测试拔刀斩专门的AOE计算
    qDebug() << "\n=== 拔刀斩AOE测试 ===";
    QPointF aoePos = engine.calculateOptimalAOEPosition(monsters, 150.0f, playerPos, 300.0f);
    int hits = engine.calculateExpectedHits(monsters, aoePos, 150.0f);
    qDebug() << "最佳AOE位置:" << aoePos;
    qDebug() << "预期命中数量:" << hits;
    
    qDebug() << "\n测试完成！";
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    testBasicFunctionality();

    return 0;
}
