#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QStackedWidget>
#include <QLabel>
#include <QTextEdit>
#include <QFrame>

class SideNavigation : public QWidget
{
    Q_OBJECT

public:
    explicit SideNavigation(QWidget* parent = nullptr);
    ~SideNavigation();

    // 页面索引
    enum PageIndex {
        LogsPage = 0,
        SettingsPage = 1,
        DebugPage = 2,
        AboutPage = 3
    };

    // 获取日志文本编辑器
    QTextEdit* getLogTextEdit() const;

    // 切换页面
    void switchToPage(PageIndex page);

signals:
    void pageChanged(int pageIndex);

private slots:
    void onNavigationButtonClicked();

private:
    void setupUI();
    void createNavigationButtons();
    void createPages();
    void updateButtonStyles();

    // UI组件
    QHBoxLayout* m_mainLayout;
    QWidget* m_navigationPanel;
    QVBoxLayout* m_navLayout;
    QStackedWidget* m_stackedWidget;

    // 导航按钮
    QPushButton* m_logsButton;
    QPushButton* m_settingsButton;
    QPushButton* m_debugButton;
    QPushButton* m_aboutButton;

    // 页面
    QWidget* m_logsPage;
    QWidget* m_settingsPage;
    QWidget* m_debugPage;
    QWidget* m_aboutPage;

    // 日志文本编辑器
    QTextEdit* m_logTextEdit;

    // 当前页面
    int m_currentPage;

    // 样式
    QString m_buttonStyle;
    QString m_activeButtonStyle;
};
