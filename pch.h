﻿// pch.h: 这是预编译标头文件。
// 下方列出的文件仅编译一次，提高了将来生成的生成性能。
// 这还将影响 IntelliSense 性能，包括代码完成和许多代码浏览功能。
// 但是，如果此处列出的文件中的任何一个在生成之间有更新，它们全部都将被重新编译。
// 请勿在此处添加要频繁更新的文件，这将使得性能优势无效。

#ifndef PCH_H
#define PCH_H



// 添加要在此处预编译的标头
#include <windows.h>
#include <string>
#include <vector>
#include <thread>
#include <functional>
#include <algorithm>
#include <map>
#include <tlhelp32.h>
#include <tchar.h>
#include <fstream>
#include <mutex>
#include <winnt.h>
#include <iostream>
#include <assert.h>
#include <cstdlib>
#include <exception>

#include "xorstr.hpp"
#include "offset.h"
#include "help.h"
//#include "Gamedata.h"



//删除指针
#define SAFEDELETE(pData) { try { delete pData; } catch (...) { assert(FALSE); } pData = NULL; } 

//删除数组
#define SAFEDELETEARRAY(pData) { try { delete [] pData; } catch (...) { assert(FALSE); } pData=NULL; } 

#endif //PCH_H
