#pragma once

#include <QObject>
#include <QPointF>
#include <QVector>
#include <QString>
#include <QMap>
#include <QtMath>

// 怪物信息结构
struct MonsterInfo {
    float x, y;           // 怪物坐标
    QString name;         // 怪物名称
    int isElite;          // 是否精英怪（0=普通，1=精英）
    float distance;       // 与角色的距离
    int id;               // 怪物ID（用于追踪）
    
    MonsterInfo() : x(0), y(0), name(""), isElite(0), distance(0), id(-1) {}
    MonsterInfo(float px, float py, const QString& n, int elite = 0)
        : x(px), y(py), name(n), isElite(elite), distance(0), id(-1) {}
};

// 技能信息结构
struct SkillInfo {
    QString name;         // 技能名称
    QString key;          // 技能键位
    bool isReady;         // 是否冷却完毕
    
    SkillInfo() : name(""), key(""), isReady(false) {}
    SkillInfo(const QString& n, const QString& k, bool ready = true)
        : name(n), key(k), isReady(ready) {}
};

// 技能模板结构（预设数据）
struct SkillTemplate {
    QString name;
    int priority;         // 优先级（1-10）
    float range;          // 攻击范围
    float aoeRadius;      // AOE半径（0表示单体）
    bool isAOE;           // 是否范围攻击
    QString description;
    
    SkillTemplate() : name(""), priority(1), range(100), aoeRadius(0), isAOE(false), description("") {}
};

// 决策结果结构
struct DecisionResult {
    QString skillKey;     // 要释放的技能键位
    QString skillName;    // 技能名称
    QPointF targetPos;    // 目标位置
    int expectedHits;     // 预期命中数量
    float efficiency;     // 效率评分
    QString strategy;     // 策略说明
    QString reason;       // 决策原因
    
    DecisionResult() : skillKey(""), skillName(""), targetPos(0,0), expectedHits(0), efficiency(0.0f), strategy(""), reason("") {}
};

// 群体分析结果
struct ClusterInfo {
    QPointF center;           // 群体中心点
    int monsterCount;         // 群体内怪物数量
    float density;            // 怪物密度
    QVector<int> monsterIds;  // 群体内怪物ID列表
    float totalPriority;      // 群体总优先级
    
    ClusterInfo() : center(0,0), monsterCount(0), density(0.0f), totalPriority(0.0f) {}
};

class SkillDecisionEngine : public QObject
{
    Q_OBJECT

public:
    explicit SkillDecisionEngine(QObject *parent = nullptr);
    
    // 主要决策接口
    DecisionResult makeDecision(const QVector<MonsterInfo>& monsters,
                               const QVector<SkillInfo>& availableSkills,
                               const QPointF& playerPos);
    
    // 公开一些有用的计算函数供测试使用
    QPointF calculateOptimalAOEPosition(const QVector<MonsterInfo>& monsters,
                                       float aoeRadius,
                                       const QPointF& playerPos,
                                       float maxRange);
    
    int calculateExpectedHits(const QVector<MonsterInfo>& monsters,
                             const QPointF& releasePos,
                             float aoeRadius);

    // 距离计算
    static float calculateDistance(const QPointF& p1, const QPointF& p2);

public slots:
    // 初始化技能数据库
    void initializeSkillDatabase();

signals:
    // 决策完成信号
    void decisionMade(const DecisionResult& result);
    
    // 调试信息信号
    void debugInfo(const QString& message);

private:
    // 技能数据库
    QMap<QString, SkillTemplate> m_skillDatabase;
    
    // 私有决策函数
    DecisionResult decideBestSkill(const QVector<MonsterInfo>& monsters,
                                  const QVector<SkillInfo>& availableSkills,
                                  const QPointF& playerPos);
    
    // 群体分析
    ClusterInfo findLargestCluster(const QVector<MonsterInfo>& monsters,
                                  float clusterRadius);
    
    // 找到最高优先级目标
    MonsterInfo findHighestPriorityTarget(const QVector<MonsterInfo>& monsters);
    
    // 过滤有效目标
    QVector<MonsterInfo> filterValidTargets(const QVector<MonsterInfo>& monsters,
                                           const QPointF& playerPos,
                                           float maxRange);
    
    // 计算怪物优先级
    float calculateMonsterPriority(const MonsterInfo& monster);
    
    // 初始化预设技能数据
    void setupDefaultSkills();
    
    // 更新怪物距离信息
    void updateMonsterDistances(QVector<MonsterInfo>& monsters,
                               const QPointF& playerPos);
    
    // 获取技能模板信息
    SkillTemplate getSkillTemplate(const QString& skillName);
};
