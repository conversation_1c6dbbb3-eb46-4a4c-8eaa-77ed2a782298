/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.15.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // F:/A_C+/QT_D/QT_D/Ico/24-bug.svg
  0x0,0x0,0x1,0x56,
  0x0,
  0x0,0x5,0x4c,0x78,0x9c,0xc5,0x94,0x4d,0x73,0x82,0x30,0x10,0x86,0xef,0xfd,0x15,
  0x99,0xf4,0x4a,0xc8,0x87,0x94,0x68,0x87,0x70,0xe8,0xa1,0xb7,0xfe,0x8,0xc4,0x14,
  0xd2,0x86,0x8f,0x92,0x28,0xf8,0xef,0xbb,0x8,0x5a,0xed,0xc1,0x9b,0x3a,0x64,0x67,
  0xf7,0x65,0x36,0xd9,0x67,0xde,0xcc,0x24,0x71,0xbb,0x2,0xd,0x95,0xad,0x9d,0xc2,
  0xa5,0xf7,0xed,0x2b,0xa5,0x7d,0xdf,0x87,0xfd,0x22,0x6c,0xba,0x82,0xa,0xc6,0x18,
  0x85,0xe,0x8c,0x4a,0x6d,0x8a,0xd2,0x2b,0x2c,0x22,0x8c,0x7a,0xb3,0xf1,0xe5,0x54,
  0xee,0x8c,0xee,0xdf,0x9a,0x41,0x61,0x86,0x18,0x12,0x11,0x2c,0x9c,0x26,0xde,0x78,
  0xab,0x53,0x50,0xeb,0x6d,0x91,0xd0,0x49,0x25,0x5,0x72,0xbe,0x6b,0xbe,0x35,0xb1,
  0xa6,0xd6,0x5f,0x8d,0xa9,0x15,0xae,0x8c,0xd7,0x1d,0x46,0x9f,0xc6,0x5a,0x85,0x9f,
  0xdf,0xe5,0xf8,0xe1,0xf3,0xb6,0x3c,0x6b,0x15,0x5e,0x6f,0xbd,0xc7,0x28,0xb7,0x99,
  0x3,0xc4,0x3a,0x27,0x26,0x6f,0x6a,0xd2,0x77,0x59,0xdb,0xc2,0xe6,0x34,0x69,0x33,
  0x5f,0xa2,0xd,0x1c,0xb6,0xc,0xe2,0x9c,0x11,0x11,0xa,0xb6,0xa,0x78,0x28,0x57,
  0x9c,0x44,0x41,0x44,0x22,0x17,0x4d,0x2a,0x0,0x75,0x9c,0x55,0x37,0xb5,0x3e,0xe,
  0xba,0x32,0xd9,0xfd,0x6c,0xb3,0xee,0xd4,0x48,0xe,0xb8,0xd6,0x40,0x52,0x98,0xb3,
  0xd3,0xef,0xa3,0x1b,0xc0,0x42,0x47,0x98,0x3f,0xa4,0x8f,0x38,0x7c,0x9,0x20,0x2c,
  0x59,0x40,0x1,0x51,0x71,0x39,0x96,0x27,0x7d,0x68,0xe0,0xf2,0x4a,0xc7,0x9d,0x88,
  0xb5,0xb5,0xa6,0x75,0x1a,0xe5,0x70,0x93,0x5c,0x80,0xdb,0x7b,0xc8,0x30,0xbc,0x3,
  0xd,0xae,0x75,0x20,0xe5,0xcd,0x51,0x66,0x88,0x34,0x19,0xcf,0x40,0x3,0x9f,0x50,
  0xf6,0x73,0x1e,0xc4,0xac,0xc7,0xbc,0xbc,0x39,0xcc,0xb8,0xf5,0x9c,0xe4,0x12,0x44,
  0xce,0x1c,0xe2,0xee,0x1c,0xf2,0x12,0x44,0x2c,0x1e,0x46,0x32,0x83,0xc4,0x97,0x8e,
  0xc4,0xf,0x73,0x24,0xfe,0xe7,0xc8,0xbd,0x48,0x68,0x1,0x1,0x6f,0x64,0xfa,0xf4,
  0xb,0x37,0x93,0xa2,0xda,
    // F:/A_C+/QT_D/QT_D/Ico/24-info.svg
  0x0,0x0,0x2,0x66,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,
  0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x3e,0x3c,0x74,0x69,0x74,0x6c,0x65,0x3e,0x32,0x34,0x20,0x69,0x6e,0x66,0x6f,
  0x3c,0x2f,0x74,0x69,0x74,0x6c,0x65,0x3e,0x3c,0x67,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x6d,0x69,0x74,0x65,
  0x72,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x46,0x37,0x46,0x37,0x46,0x37,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x62,0x75,0x74,0x74,0x22,0x20,0x63,0x6c,0x61,0x73,0x73,0x3d,0x22,0x6e,
  0x63,0x2d,0x69,0x63,0x6f,0x6e,0x2d,0x77,0x72,0x61,0x70,0x70,0x65,0x72,0x22,0x3e,
  0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x32,0x22,0x20,
  0x63,0x79,0x3d,0x22,0x31,0x32,0x22,0x20,0x72,0x3d,0x22,0x31,0x30,0x22,0x20,0x66,
  0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x46,0x37,0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x73,0x71,0x75,0x61,
  0x72,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,
  0x6c,0x69,0x6d,0x69,0x74,0x3d,0x22,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x3e,0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,0x3d,0x22,0x31,
  0x32,0x22,0x20,0x79,0x31,0x3d,0x22,0x31,0x36,0x22,0x20,0x78,0x32,0x3d,0x22,0x31,
  0x32,0x22,0x20,0x79,0x32,0x3d,0x22,0x31,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,
  0x46,0x37,0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,
  0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x73,0x71,0x75,0x61,0x72,0x65,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,0x6c,0x69,0x6d,0x69,
  0x74,0x3d,0x22,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,
  0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,0x6c,0x69,0x6e,0x65,0x3e,0x3c,
  0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,0x3d,0x22,0x31,0x32,0x22,0x20,0x79,0x31,0x3d,
  0x22,0x38,0x22,0x20,0x78,0x32,0x3d,0x22,0x31,0x32,0x2e,0x30,0x31,0x22,0x20,0x79,
  0x32,0x3d,0x22,0x38,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x37,0x46,0x37,0x46,
  0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x73,0x71,0x75,0x61,0x72,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,0x6c,0x69,0x6d,0x69,0x74,0x3d,0x22,0x31,0x30,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x3e,0x3c,0x2f,0x6c,0x69,0x6e,0x65,0x3e,0x3c,0x2f,0x67,0x3e,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // F:/A_C+/QT_D/QT_D/Ico/24-document.svg
  0x0,0x0,0x3,0x9d,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,
  0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x3e,0x3c,0x74,0x69,0x74,0x6c,0x65,0x3e,0x32,0x34,0x20,0x64,0x6f,0x63,0x75,
  0x6d,0x65,0x6e,0x74,0x3c,0x2f,0x74,0x69,0x74,0x6c,0x65,0x3e,0x3c,0x67,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,
  0x6d,0x69,0x74,0x65,0x72,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x46,0x37,
  0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x63,0x61,0x70,0x3d,0x22,0x62,0x75,0x74,0x74,0x22,0x20,0x63,0x6c,0x61,0x73,
  0x73,0x3d,0x22,0x6e,0x63,0x2d,0x69,0x63,0x6f,0x6e,0x2d,0x77,0x72,0x61,0x70,0x70,
  0x65,0x72,0x22,0x3e,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x34,
  0x2c,0x32,0x48,0x36,0x41,0x32,0x2c,0x32,0x2c,0x30,0x2c,0x30,0x2c,0x30,0x2c,0x34,
  0x2c,0x34,0x56,0x32,0x30,0x61,0x32,0x2c,0x32,0x2c,0x30,0x2c,0x30,0x2c,0x30,0x2c,
  0x32,0x2c,0x32,0x48,0x31,0x38,0x61,0x32,0x2c,0x32,0x2c,0x30,0x2c,0x30,0x2c,0x30,
  0x2c,0x32,0x2d,0x32,0x56,0x38,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,
  0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x37,
  0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x63,0x61,0x70,0x3d,0x22,0x73,0x71,0x75,0x61,0x72,0x65,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,0x6c,0x69,0x6d,0x69,0x74,0x3d,
  0x22,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,0x70,0x61,0x74,0x68,0x3e,0x3c,0x70,0x6f,
  0x6c,0x79,0x6c,0x69,0x6e,0x65,0x20,0x70,0x6f,0x69,0x6e,0x74,0x73,0x3d,0x22,0x31,
  0x34,0x2c,0x32,0x20,0x31,0x34,0x2c,0x38,0x20,0x32,0x30,0x2c,0x38,0x22,0x20,0x66,
  0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x46,0x37,0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x73,0x71,0x75,0x61,
  0x72,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,
  0x6c,0x69,0x6d,0x69,0x74,0x3d,0x22,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,0x70,0x6f,
  0x6c,0x79,0x6c,0x69,0x6e,0x65,0x3e,0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,0x3d,
  0x22,0x31,0x36,0x22,0x20,0x79,0x31,0x3d,0x22,0x31,0x33,0x22,0x20,0x78,0x32,0x3d,
  0x22,0x38,0x22,0x20,0x79,0x32,0x3d,0x22,0x31,0x33,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,
  0x23,0x46,0x37,0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x73,0x71,0x75,0x61,0x72,0x65,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,0x6c,0x69,0x6d,
  0x69,0x74,0x3d,0x22,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,
  0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,0x6c,0x69,0x6e,0x65,0x3e,
  0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,0x3d,0x22,0x31,0x36,0x22,0x20,0x79,0x31,
  0x3d,0x22,0x31,0x37,0x22,0x20,0x78,0x32,0x3d,0x22,0x38,0x22,0x20,0x79,0x32,0x3d,
  0x22,0x31,0x37,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x37,0x46,0x37,0x46,0x37,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x73,0x71,0x75,0x61,0x72,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6d,0x69,0x74,0x65,0x72,0x6c,0x69,0x6d,0x69,0x74,0x3d,0x22,0x31,0x30,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x3e,0x3c,0x2f,0x6c,0x69,0x6e,0x65,0x3e,0x3c,0x70,0x6f,0x6c,0x79,0x6c,0x69,
  0x6e,0x65,0x20,0x70,0x6f,0x69,0x6e,0x74,0x73,0x3d,0x22,0x31,0x30,0x2c,0x39,0x20,
  0x39,0x2c,0x39,0x20,0x38,0x2c,0x39,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,
  0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x37,
  0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x63,0x61,0x70,0x3d,0x22,0x73,0x71,0x75,0x61,0x72,0x65,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,0x6c,0x69,0x6d,0x69,0x74,0x3d,
  0x22,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,0x70,0x6f,0x6c,0x79,0x6c,0x69,0x6e,0x65,
  0x3e,0x3c,0x2f,0x67,0x3e,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // F:/A_C+/QT_D/QT_D/Ico/24-cogwheel.svg
  0x0,0x0,0x3,0xcc,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,
  0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x3e,0x3c,0x74,0x69,0x74,0x6c,0x65,0x3e,0x32,0x34,0x20,0x63,0x6f,0x67,0x77,
  0x68,0x65,0x65,0x6c,0x3c,0x2f,0x74,0x69,0x74,0x6c,0x65,0x3e,0x3c,0x67,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,
  0x6d,0x69,0x74,0x65,0x72,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x46,0x37,
  0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x63,0x61,0x70,0x3d,0x22,0x62,0x75,0x74,0x74,0x22,0x20,0x63,0x6c,0x61,0x73,
  0x73,0x3d,0x22,0x6e,0x63,0x2d,0x69,0x63,0x6f,0x6e,0x2d,0x77,0x72,0x61,0x70,0x70,
  0x65,0x72,0x22,0x3e,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,
  0x31,0x32,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x32,0x22,0x20,0x72,0x3d,0x22,0x33,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x37,0x46,0x37,0x46,0x37,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x73,
  0x71,0x75,0x61,0x72,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,
  0x74,0x65,0x72,0x6c,0x69,0x6d,0x69,0x74,0x3d,0x22,0x31,0x30,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x64,
  0x61,0x74,0x61,0x2d,0x63,0x6f,0x6c,0x6f,0x72,0x3d,0x22,0x63,0x6f,0x6c,0x6f,0x72,
  0x2d,0x32,0x22,0x3e,0x3c,0x2f,0x63,0x69,0x72,0x63,0x6c,0x65,0x3e,0x3c,0x70,0x61,
  0x74,0x68,0x20,0x64,0x3d,0x22,0x6d,0x32,0x32,0x2c,0x31,0x33,0x2e,0x32,0x35,0x76,
  0x2d,0x32,0x2e,0x35,0x6c,0x2d,0x32,0x2e,0x33,0x31,0x38,0x2d,0x2e,0x39,0x36,0x36,
  0x63,0x2d,0x2e,0x31,0x36,0x37,0x2d,0x2e,0x35,0x38,0x31,0x2d,0x2e,0x33,0x39,0x35,
  0x2d,0x31,0x2e,0x31,0x33,0x35,0x2d,0x2e,0x36,0x38,0x32,0x2d,0x31,0x2e,0x36,0x35,
  0x34,0x6c,0x2e,0x39,0x35,0x34,0x2d,0x32,0x2e,0x33,0x31,0x38,0x2d,0x31,0x2e,0x37,
  0x36,0x38,0x2d,0x31,0x2e,0x37,0x36,0x38,0x2d,0x32,0x2e,0x33,0x31,0x38,0x2e,0x39,
  0x35,0x34,0x63,0x2d,0x2e,0x35,0x31,0x38,0x2d,0x2e,0x32,0x38,0x37,0x2d,0x31,0x2e,
  0x30,0x37,0x33,0x2d,0x2e,0x35,0x31,0x35,0x2d,0x31,0x2e,0x36,0x35,0x34,0x2d,0x2e,
  0x36,0x38,0x32,0x6c,0x2d,0x2e,0x39,0x36,0x36,0x2d,0x32,0x2e,0x33,0x31,0x38,0x68,
  0x2d,0x32,0x2e,0x35,0x6c,0x2d,0x2e,0x39,0x36,0x36,0x2c,0x32,0x2e,0x33,0x31,0x38,
  0x63,0x2d,0x2e,0x35,0x38,0x31,0x2e,0x31,0x36,0x37,0x2d,0x31,0x2e,0x31,0x33,0x35,
  0x2e,0x33,0x39,0x35,0x2d,0x31,0x2e,0x36,0x35,0x34,0x2e,0x36,0x38,0x32,0x6c,0x2d,
  0x32,0x2e,0x33,0x31,0x38,0x2d,0x2e,0x39,0x35,0x34,0x2d,0x31,0x2e,0x37,0x36,0x38,
  0x2c,0x31,0x2e,0x37,0x36,0x38,0x2e,0x39,0x35,0x34,0x2c,0x32,0x2e,0x33,0x31,0x38,
  0x63,0x2d,0x2e,0x32,0x38,0x37,0x2e,0x35,0x31,0x38,0x2d,0x2e,0x35,0x31,0x35,0x2c,
  0x31,0x2e,0x30,0x37,0x33,0x2d,0x2e,0x36,0x38,0x32,0x2c,0x31,0x2e,0x36,0x35,0x34,
  0x6c,0x2d,0x32,0x2e,0x33,0x31,0x38,0x2e,0x39,0x36,0x36,0x76,0x32,0x2e,0x35,0x6c,
  0x32,0x2e,0x33,0x31,0x38,0x2e,0x39,0x36,0x36,0x63,0x2e,0x31,0x36,0x37,0x2e,0x35,
  0x38,0x31,0x2e,0x33,0x39,0x35,0x2c,0x31,0x2e,0x31,0x33,0x35,0x2e,0x36,0x38,0x32,
  0x2c,0x31,0x2e,0x36,0x35,0x34,0x6c,0x2d,0x2e,0x39,0x35,0x34,0x2c,0x32,0x2e,0x33,
  0x31,0x38,0x2c,0x31,0x2e,0x37,0x36,0x38,0x2c,0x31,0x2e,0x37,0x36,0x38,0x2c,0x32,
  0x2e,0x33,0x31,0x38,0x2d,0x2e,0x39,0x35,0x34,0x63,0x2e,0x35,0x31,0x38,0x2e,0x32,
  0x38,0x37,0x2c,0x31,0x2e,0x30,0x37,0x33,0x2e,0x35,0x31,0x35,0x2c,0x31,0x2e,0x36,
  0x35,0x34,0x2e,0x36,0x38,0x32,0x6c,0x2e,0x39,0x36,0x36,0x2c,0x32,0x2e,0x33,0x31,
  0x38,0x68,0x32,0x2e,0x35,0x6c,0x2e,0x39,0x36,0x36,0x2d,0x32,0x2e,0x33,0x31,0x38,
  0x63,0x2e,0x35,0x38,0x31,0x2d,0x2e,0x31,0x36,0x37,0x2c,0x31,0x2e,0x31,0x33,0x35,
  0x2d,0x2e,0x33,0x39,0x35,0x2c,0x31,0x2e,0x36,0x35,0x34,0x2d,0x2e,0x36,0x38,0x32,
  0x6c,0x32,0x2e,0x33,0x31,0x38,0x2e,0x39,0x35,0x34,0x2c,0x31,0x2e,0x37,0x36,0x38,
  0x2d,0x31,0x2e,0x37,0x36,0x38,0x2d,0x2e,0x39,0x35,0x34,0x2d,0x32,0x2e,0x33,0x31,
  0x38,0x63,0x2e,0x32,0x38,0x37,0x2d,0x2e,0x35,0x31,0x38,0x2e,0x35,0x31,0x35,0x2d,
  0x31,0x2e,0x30,0x37,0x33,0x2e,0x36,0x38,0x32,0x2d,0x31,0x2e,0x36,0x35,0x34,0x6c,
  0x32,0x2e,0x33,0x31,0x38,0x2d,0x2e,0x39,0x36,0x36,0x5a,0x22,0x20,0x66,0x69,0x6c,
  0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,
  0x22,0x23,0x46,0x37,0x46,0x37,0x46,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x73,0x71,0x75,0x61,0x72,0x65,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6d,0x69,0x74,0x65,0x72,0x6c,0x69,
  0x6d,0x69,0x74,0x3d,0x22,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,0x70,0x61,0x74,0x68,
  0x3e,0x3c,0x2f,0x67,0x3e,0x3c,0x2f,0x73,0x76,0x67,0x3e,
  
};

static const unsigned char qt_resource_name[] = {
  // QT_D
  0x0,0x4,
  0x0,0x5,0x6a,0x34,
  0x0,0x51,
  0x0,0x54,0x0,0x5f,0x0,0x44,
    // Ico
  0x0,0x3,
  0x0,0x0,0x4f,0x9f,
  0x0,0x49,
  0x0,0x63,0x0,0x6f,
    // 24-bug.svg
  0x0,0xa,
  0x9,0xc0,0xf4,0xa7,
  0x0,0x32,
  0x0,0x34,0x0,0x2d,0x0,0x62,0x0,0x75,0x0,0x67,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // 24-info.svg
  0x0,0xb,
  0x4,0xbc,0xb2,0xc7,
  0x0,0x32,
  0x0,0x34,0x0,0x2d,0x0,0x69,0x0,0x6e,0x0,0x66,0x0,0x6f,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // 24-document.svg
  0x0,0xf,
  0xf,0x3c,0xdf,0x67,
  0x0,0x32,
  0x0,0x34,0x0,0x2d,0x0,0x64,0x0,0x6f,0x0,0x63,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x6e,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // 24-cogwheel.svg
  0x0,0xf,
  0xc,0xf4,0x54,0xc7,
  0x0,0x32,
  0x0,0x34,0x0,0x2d,0x0,0x63,0x0,0x6f,0x0,0x67,0x0,0x77,0x0,0x68,0x0,0x65,0x0,0x65,0x0,0x6c,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/QT_D
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/QT_D/Ico
  0x0,0x0,0x0,0xe,0x0,0x2,0x0,0x0,0x0,0x4,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/QT_D/Ico/24-info.svg
  0x0,0x0,0x0,0x34,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x1,0x5a,
0x0,0x0,0x1,0x97,0x93,0x2f,0x8,0xb6,
  // :/QT_D/Ico/24-bug.svg
  0x0,0x0,0x0,0x1a,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0x93,0x2e,0xe2,0x1d,
  // :/QT_D/Ico/24-cogwheel.svg
  0x0,0x0,0x0,0x74,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0x65,
0x0,0x0,0x1,0x97,0x8e,0x78,0x6e,0x4a,
  // :/QT_D/Ico/24-document.svg
  0x0,0x0,0x0,0x50,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x3,0xc4,
0x0,0x0,0x1,0x97,0x93,0x2e,0xab,0xa0,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_QT_D)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_QT_D)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_QT_D)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_QT_D)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_QT_D)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_QT_D)(); }
   } dummy;
}
