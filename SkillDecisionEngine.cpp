#include "SkillDecisionEngine.h"
#include <QDebug>
#include <algorithm>
#include <QtMath>

SkillDecisionEngine::SkillDecisionEngine(QObject *parent)
    : QObject(parent)
{
    initializeSkillDatabase();
}

SkillDecisionEngine::~SkillDecisionEngine()
{
}

void SkillDecisionEngine::initializeSkillDatabase()
{
    setupDefaultSkills();
    emit debugInfo("技能数据库初始化完成");
}

void SkillDecisionEngine::setupDefaultSkills()
{
    // 清空现有数据
    m_skillDatabase.clear();
    
    // 拔刀斩 - 经典AOE技能
    SkillTemplate battoSlash;
    battoSlash.name = "拔刀斩";
    battoSlash.priority = 8;
    battoSlash.range = 300.0f;
    battoSlash.aoeRadius = 150.0f;
    battoSlash.isAOE = true;
    battoSlash.description = "范围攻击技能，适合群体怪物";
    m_skillDatabase["拔刀斩"] = battoSlash;
    
    // 普通攻击
    SkillTemplate normalAttack;
    normalAttack.name = "普通攻击";
    normalAttack.priority = 3;
    normalAttack.range = 120.0f;
    normalAttack.aoeRadius = 0;
    normalAttack.isAOE = false;
    normalAttack.description = "基础攻击";
    m_skillDatabase["普通攻击"] = normalAttack;
    
    // 上挑
    SkillTemplate upperSlash;
    upperSlash.name = "上挑";
    upperSlash.priority = 5;
    upperSlash.range = 150.0f;
    upperSlash.aoeRadius = 80.0f;
    upperSlash.isAOE = true;
    upperSlash.description = "小范围攻击技能";
    m_skillDatabase["上挑"] = upperSlash;
    
    // 鬼斩
    SkillTemplate ghostSlash;
    ghostSlash.name = "鬼斩";
    ghostSlash.priority = 6;
    ghostSlash.range = 200.0f;
    ghostSlash.aoeRadius = 0;
    ghostSlash.isAOE = false;
    ghostSlash.description = "单体高伤害技能";
    m_skillDatabase["鬼斩"] = ghostSlash;
    
    // 波动剑
    SkillTemplate waveSword;
    waveSword.name = "波动剑";
    waveSword.priority = 7;
    waveSword.range = 400.0f;
    waveSword.aoeRadius = 100.0f;
    waveSword.isAOE = true;
    waveSword.description = "远程范围攻击";
    m_skillDatabase["波动剑"] = waveSword;
    
    emit debugInfo(QString("已加载 %1 个技能模板").arg(m_skillDatabase.size()));
}

float SkillDecisionEngine::calculateDistance(const QPointF& p1, const QPointF& p2)
{
    float dx = p1.x() - p2.x();
    float dy = p1.y() - p2.y();
    return qSqrt(dx * dx + dy * dy);
}

void SkillDecisionEngine::updateMonsterDistances(QVector<MonsterInfo>& monsters, const QPointF& playerPos)
{
    for (auto& monster : monsters) {
        monster.distance = calculateDistance(QPointF(monster.x, monster.y), playerPos);
    }
}

SkillTemplate SkillDecisionEngine::getSkillTemplate(const QString& skillName)
{
    if (m_skillDatabase.contains(skillName)) {
        return m_skillDatabase[skillName];
    }
    
    // 返回默认模板
    SkillTemplate defaultTemplate;
    defaultTemplate.name = skillName;
    defaultTemplate.priority = 3;
    defaultTemplate.range = 150.0f;
    defaultTemplate.aoeRadius = 0;
    defaultTemplate.isAOE = false;
    defaultTemplate.description = "未知技能";
    
    return defaultTemplate;
}

DecisionResult SkillDecisionEngine::makeDecision(const QVector<MonsterInfo>& monsters, 
                                                const QVector<SkillInfo>& skills,
                                                const QPointF& playerPos)
{
    DecisionResult result;
    
    // 检查输入数据
    if (monsters.isEmpty()) {
        result.reason = "没有发现怪物";
        result.strategy = "等待目标";
        emit debugInfo("决策失败：没有怪物数据");
        return result;
    }
    
    if (skills.isEmpty()) {
        result.reason = "没有可用技能";
        result.strategy = "等待技能冷却";
        emit debugInfo("决策失败：没有技能数据");
        return result;
    }
    
    // 更新怪物距离
    QVector<MonsterInfo> updatedMonsters = monsters;
    updateMonsterDistances(updatedMonsters, playerPos);
    
    // 过滤可用技能
    QVector<SkillInfo> availableSkills;
    for (const auto& skill : skills) {
        if (skill.isReady) {
            availableSkills.append(skill);
        }
    }
    
    if (availableSkills.isEmpty()) {
        result.reason = "所有技能都在冷却中";
        result.strategy = "等待技能冷却";
        emit debugInfo("决策失败：没有可用技能");
        return result;
    }
    
    // 执行决策
    result = decideBestSkill(updatedMonsters, availableSkills, playerPos);
    
    emit decisionMade(result);
    emit debugInfo(QString("决策完成：%1 -> %2").arg(result.skillName).arg(result.strategy));
    
    return result;
}

DecisionResult SkillDecisionEngine::decideBestSkill(const QVector<MonsterInfo>& monsters,
                                                   const QVector<SkillInfo>& availableSkills,
                                                   const QPointF& playerPos)
{
    DecisionResult bestResult;
    float bestScore = -1.0f;
    
    // 遍历所有可用技能，计算每个技能的效果
    for (const auto& skill : availableSkills) {
        SkillTemplate skillTemplate = getSkillTemplate(skill.name);
        
        // 过滤该技能范围内的有效目标
        QVector<MonsterInfo> validTargets = filterValidTargets(monsters, playerPos, skillTemplate.range);
        
        if (validTargets.isEmpty()) {
            continue; // 没有有效目标，跳过这个技能
        }
        
        DecisionResult currentResult;
        currentResult.skillKey = skill.key;
        currentResult.skillName = skill.name;
        
        if (skillTemplate.isAOE && validTargets.size() >= 2) {
            // AOE技能：计算最佳群体攻击位置
            currentResult.targetPos = calculateOptimalAOEPosition(validTargets, 
                                                                 skillTemplate.aoeRadius, 
                                                                 playerPos, 
                                                                 skillTemplate.range);
            currentResult.expectedHits = calculateExpectedHits(validTargets, 
                                                              currentResult.targetPos, 
                                                              skillTemplate.aoeRadius);
            currentResult.strategy = QString("AOE攻击 - 预期命中%1个目标").arg(currentResult.expectedHits);
            
            // AOE效率计算：命中数量 * 技能优先级
            currentResult.efficiency = currentResult.expectedHits * skillTemplate.priority * 10.0f;
            
        } else {
            // 单体技能：选择最高优先级目标
            MonsterInfo target = findHighestPriorityTarget(validTargets);
            currentResult.targetPos = QPointF(target.x, target.y);
            currentResult.expectedHits = 1;
            currentResult.strategy = QString("单体攻击 - 目标: %1").arg(target.name);
            
            // 单体效率计算：目标优先级 * 技能优先级
            float targetPriority = calculateMonsterPriority(target);
            currentResult.efficiency = targetPriority * skillTemplate.priority;
        }
        
        currentResult.reason = QString("技能优先级:%1, 预期效率:%.1f")
                              .arg(skillTemplate.priority)
                              .arg(currentResult.efficiency);
        
        // 选择最佳技能
        if (currentResult.efficiency > bestScore) {
            bestScore = currentResult.efficiency;
            bestResult = currentResult;
        }
    }
    
    return bestResult;
}

QPointF SkillDecisionEngine::calculateOptimalAOEPosition(const QVector<MonsterInfo>& monsters,
                                                        float aoeRadius,
                                                        const QPointF& playerPos,
                                                        float maxRange)
{
    QPointF bestPos = playerPos;
    int maxHits = 0;

    // 方法1：遍历每个怪物位置作为潜在释放点
    for (const auto& monster : monsters) {
        QPointF testPos(monster.x, monster.y);

        // 检查是否在技能释放范围内
        if (calculateDistance(playerPos, testPos) > maxRange) {
            continue;
        }

        // 计算这个位置能命中多少怪物
        int hitCount = calculateExpectedHits(monsters, testPos, aoeRadius);

        if (hitCount > maxHits) {
            maxHits = hitCount;
            bestPos = testPos;
        }
    }

    // 方法2：如果没找到好位置，尝试怪物群体中心
    if (maxHits <= 1 && monsters.size() >= 3) {
        ClusterInfo cluster = findLargestCluster(monsters, aoeRadius);
        if (cluster.monsterCount >= 2) {
            float distanceToCenter = calculateDistance(playerPos, cluster.center);
            if (distanceToCenter <= maxRange) {
                bestPos = cluster.center;
            }
        }
    }

    return bestPos;
}

ClusterInfo SkillDecisionEngine::findLargestCluster(const QVector<MonsterInfo>& monsters,
                                                   float clusterRadius)
{
    ClusterInfo bestCluster;

    for (int i = 0; i < monsters.size(); ++i) {
        ClusterInfo currentCluster;
        currentCluster.center = QPointF(monsters[i].x, monsters[i].y);

        // 计算以当前怪物为中心的群体
        for (int j = 0; j < monsters.size(); ++j) {
            QPointF monsterPos(monsters[j].x, monsters[j].y);
            float distance = calculateDistance(currentCluster.center, monsterPos);

            if (distance <= clusterRadius) {
                currentCluster.monsterCount++;
                currentCluster.monsterIds.append(j);
                currentCluster.totalPriority += calculateMonsterPriority(monsters[j]);
            }
        }

        // 计算密度
        if (clusterRadius > 0) {
            float area = M_PI * clusterRadius * clusterRadius;
            currentCluster.density = currentCluster.monsterCount / area;
        }

        // 选择最佳群体（优先考虑怪物数量，其次考虑优先级）
        bool isBetter = (currentCluster.monsterCount > bestCluster.monsterCount) ||
                       (currentCluster.monsterCount == bestCluster.monsterCount &&
                        currentCluster.totalPriority > bestCluster.totalPriority);

        if (isBetter) {
            bestCluster = currentCluster;
        }
    }

    return bestCluster;
}

int SkillDecisionEngine::calculateExpectedHits(const QVector<MonsterInfo>& monsters,
                                              const QPointF& releasePos,
                                              float aoeRadius)
{
    int hitCount = 0;

    for (const auto& monster : monsters) {
        QPointF monsterPos(monster.x, monster.y);
        float distance = calculateDistance(releasePos, monsterPos);

        if (distance <= aoeRadius) {
            hitCount++;
        }
    }

    return hitCount;
}

MonsterInfo SkillDecisionEngine::findHighestPriorityTarget(const QVector<MonsterInfo>& monsters)
{
    if (monsters.isEmpty()) {
        return MonsterInfo();
    }

    MonsterInfo bestTarget = monsters[0];
    float bestPriority = calculateMonsterPriority(bestTarget);

    for (const auto& monster : monsters) {
        float priority = calculateMonsterPriority(monster);

        // 优先级相同时，选择距离更近的
        if (priority > bestPriority ||
           (priority == bestPriority && monster.distance < bestTarget.distance)) {
            bestPriority = priority;
            bestTarget = monster;
        }
    }

    return bestTarget;
}

QVector<MonsterInfo> SkillDecisionEngine::filterValidTargets(const QVector<MonsterInfo>& monsters,
                                                            const QPointF& playerPos,
                                                            float maxRange)
{
    QVector<MonsterInfo> validTargets;

    for (const auto& monster : monsters) {
        QPointF monsterPos(monster.x, monster.y);
        float distance = calculateDistance(playerPos, monsterPos);

        if (distance <= maxRange) {
            validTargets.append(monster);
        }
    }

    return validTargets;
}

float SkillDecisionEngine::calculateMonsterPriority(const MonsterInfo& monster)
{
    float priority = 1.0f; // 基础优先级

    // 精英怪优先级更高
    if (monster.isElite == 1) {
        priority += 5.0f;
    }

    // 根据名称判断特殊怪物
    QString name = monster.name.toLower();
    if (name.contains("boss") || name.contains("领主") || name.contains("王")) {
        priority += 10.0f; // BOSS最高优先级
    } else if (name.contains("精英") || name.contains("队长")) {
        priority += 3.0f;
    }

    // 距离因子：距离越近优先级稍高
    if (monster.distance > 0) {
        priority += (300.0f - qMin(monster.distance, 300.0f)) / 300.0f;
    }

    return priority;
}
