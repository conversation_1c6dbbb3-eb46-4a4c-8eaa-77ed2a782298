<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
<Qt_DEFINES_>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;NDEBUG;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB</Qt_DEFINES_>
<Qt_INCLUDEPATH_>D:\Qt\5.15.2\msvc2019\include;D:\Qt\5.15.2\msvc2019\include\QtWidgets;D:\Qt\5.15.2\msvc2019\include\QtGui;D:\Qt\5.15.2\msvc2019\include\QtANGLE;D:\Qt\5.15.2\msvc2019\include\QtCore;D:\Qt\5.15.2\msvc2019\mkspecs\win32-msvc</Qt_INCLUDEPATH_>
<Qt_STDCPP_></Qt_STDCPP_>
<Qt_RUNTIME_>MultiThreadedDLL</Qt_RUNTIME_>
<Qt_CL_OPTIONS_>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus</Qt_CL_OPTIONS_>
<Qt_LIBS_>D:\Qt\5.15.2\msvc2019\lib\Qt5Widgets.lib;D:\Qt\5.15.2\msvc2019\lib\Qt5Gui.lib;D:\Qt\5.15.2\msvc2019\lib\Qt5Core.lib;D:\Qt\5.15.2\msvc2019\lib\qtmain.lib;shell32.lib</Qt_LIBS_>
<Qt_LINK_OPTIONS_>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"</Qt_LINK_OPTIONS_>
<QMake_QT_SYSROOT_></QMake_QT_SYSROOT_>
<QMake_QT_INSTALL_PREFIX_>D:/Qt/5.15.2/msvc2019</QMake_QT_INSTALL_PREFIX_>
<QMake_QT_INSTALL_ARCHDATA_>D:/Qt/5.15.2/msvc2019</QMake_QT_INSTALL_ARCHDATA_>
<QMake_QT_INSTALL_DATA_>D:/Qt/5.15.2/msvc2019</QMake_QT_INSTALL_DATA_>
<QMake_QT_INSTALL_DOCS_>D:/Qt/Docs/Qt-5.15.2</QMake_QT_INSTALL_DOCS_>
<QMake_QT_INSTALL_HEADERS_>D:/Qt/5.15.2/msvc2019/include</QMake_QT_INSTALL_HEADERS_>
<QMake_QT_INSTALL_LIBS_>D:/Qt/5.15.2/msvc2019/lib</QMake_QT_INSTALL_LIBS_>
<QMake_QT_INSTALL_LIBEXECS_>D:/Qt/5.15.2/msvc2019/bin</QMake_QT_INSTALL_LIBEXECS_>
<QMake_QT_INSTALL_BINS_>D:/Qt/5.15.2/msvc2019/bin</QMake_QT_INSTALL_BINS_>
<QMake_QT_INSTALL_TESTS_>D:/Qt/5.15.2/msvc2019/tests</QMake_QT_INSTALL_TESTS_>
<QMake_QT_INSTALL_PLUGINS_>D:/Qt/5.15.2/msvc2019/plugins</QMake_QT_INSTALL_PLUGINS_>
<QMake_QT_INSTALL_IMPORTS_>D:/Qt/5.15.2/msvc2019/imports</QMake_QT_INSTALL_IMPORTS_>
<QMake_QT_INSTALL_QML_>D:/Qt/5.15.2/msvc2019/qml</QMake_QT_INSTALL_QML_>
<QMake_QT_INSTALL_TRANSLATIONS_>D:/Qt/5.15.2/msvc2019/translations</QMake_QT_INSTALL_TRANSLATIONS_>
<QMake_QT_INSTALL_CONFIGURATION_></QMake_QT_INSTALL_CONFIGURATION_>
<QMake_QT_INSTALL_EXAMPLES_>D:/Qt/Examples/Qt-5.15.2</QMake_QT_INSTALL_EXAMPLES_>
<QMake_QT_INSTALL_DEMOS_>D:/Qt/Examples/Qt-5.15.2</QMake_QT_INSTALL_DEMOS_>
<QMake_QT_HOST_PREFIX_>D:/Qt/5.15.2/msvc2019</QMake_QT_HOST_PREFIX_>
<QMake_QT_HOST_DATA_>D:/Qt/5.15.2/msvc2019</QMake_QT_HOST_DATA_>
<QMake_QT_HOST_BINS_>D:/Qt/5.15.2/msvc2019/bin</QMake_QT_HOST_BINS_>
<QMake_QT_HOST_LIBS_>D:/Qt/5.15.2/msvc2019/lib</QMake_QT_HOST_LIBS_>
<QMake_QMAKE_SPEC_>win32-msvc</QMake_QMAKE_SPEC_>
<QMake_QMAKE_XSPEC_>win32-msvc</QMake_QMAKE_XSPEC_>
<QMake_QMAKE_VERSION_>3.1</QMake_QMAKE_VERSION_>
<QMake_QT_VERSION_>5.15.2</QMake_QT_VERSION_>
<QtBkup_QtHash>jZFBbgMhDEWv4hNkmkhZdJEFsV3qlAFkm1ZRVeX+twgzmmqkbsqCDfz37f/5FuLs4vfL+XA8H06P2T7x9HJ8BSzKEJvAl1BkN7hKXg/xm2S2y1S13Bh9Eud5vRSXkqOWVidMWOYqibuKuxDZrOiuMpCMqRHX4O+jVoFoHRHSBpNoX6CosIE5Ya2jVink2EJk85ApKIG2XsPMo/wmT3LVoHfA9Ch1ST9cy55lA6F7/UP32B/TDvbyOPfvwyX+8ja2wx+X3/HKiYPxzxM=</QtBkup_QtHash>
    <QtVersion>5.15.2</QtVersion>
    <QtVersionMajor>5</QtVersionMajor>
    <QtVersionMinor>15</QtVersionMinor>
    <QtVersionPatch>2</QtVersionPatch>
  </PropertyGroup>
</Project>
