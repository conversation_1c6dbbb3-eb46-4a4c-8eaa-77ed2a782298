/********************************************************************************
** Form generated from reading UI file 'QT_D.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_QT_D_H
#define UI_QT_D_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_QT_DClass
{
public:
    QMenuBar *menuBar;
    QToolBar *mainToolBar;
    QWidget *centralWidget;
    QStatusBar *statusBar;

    void setupUi(QMainWindow *QT_DClass)
    {
        if (QT_DClass->objectName().isEmpty())
            QT_DClass->setObjectName(QString::fromUtf8("QT_DClass"));
        QT_DClass->resize(600, 400);
        menuBar = new QMenuBar(QT_DClass);
        menuBar->setObjectName(QString::fromUtf8("menuBar"));
        QT_DClass->setMenuBar(menuBar);
        mainToolBar = new QToolBar(QT_DClass);
        mainToolBar->setObjectName(QString::fromUtf8("mainToolBar"));
        QT_DClass->addToolBar(mainToolBar);
        centralWidget = new QWidget(QT_DClass);
        centralWidget->setObjectName(QString::fromUtf8("centralWidget"));
        QT_DClass->setCentralWidget(centralWidget);
        statusBar = new QStatusBar(QT_DClass);
        statusBar->setObjectName(QString::fromUtf8("statusBar"));
        QT_DClass->setStatusBar(statusBar);

        retranslateUi(QT_DClass);

        QMetaObject::connectSlotsByName(QT_DClass);
    } // setupUi

    void retranslateUi(QMainWindow *QT_DClass)
    {
        QT_DClass->setWindowTitle(QCoreApplication::translate("QT_DClass", "QT_D", nullptr));
    } // retranslateUi

};

namespace Ui {
    class QT_DClass: public Ui_QT_DClass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_QT_D_H
