#include "ModernStyle.h"

// 2025年现代化配色方案
const QString ModernStyle::PRIMARY_COLOR = "#007acc";
const QString ModernStyle::SECONDARY_COLOR = "#2d2d2d";
const QString ModernStyle::ACCENT_COLOR = "#00d4aa";
const QString ModernStyle::BACKGROUND_COLOR = "#1e1e1e";
const QString ModernStyle::SURFACE_COLOR = "#252525";
const QString ModernStyle::TEXT_COLOR = "#ffffff";
const QString ModernStyle::TEXT_SECONDARY_COLOR = "#b0b0b0";
const QString ModernStyle::BORDER_COLOR = "#3c3c3c";
const QString ModernStyle::HOVER_COLOR = "#404040";
const QString ModernStyle::PRESSED_COLOR = "#505050";
const QString ModernStyle::SUCCESS_COLOR = "#27ae60";
const QString ModernStyle::WARNING_COLOR = "#f39c12";
const QString ModernStyle::ERROR_COLOR = "#e74c3c";

QString ModernStyle::getMainWindowStyle()
{
    return QString(
        "QMainWindow {"
        "    background-color: %1;"
        "    border: 1px solid %2;"
        "    border-radius: 8px;"
        "}"
    ).arg(BACKGROUND_COLOR, BORDER_COLOR);
}

QString ModernStyle::getTitleBarStyle()
{
    return QString(
        "QWidget#titleBar {"
        "    background-color: %1;"
        "    border-top-left-radius: 7px;"
        "    border-top-right-radius: 7px;"
        "    border-bottom: 1px solid %2;"
        "}"
        "QLabel#titleIcon {"
        "    background-color: %3;"
        "    border-radius: 12px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 12px;"
        "}"
        "QLabel#titleLabel {"
        "    color: %4;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    background-color: transparent;"
        "}"
    ).arg(SECONDARY_COLOR, BORDER_COLOR, PRIMARY_COLOR, TEXT_COLOR);
}

QString ModernStyle::getButtonStyle()
{
    return QString(
        "QPushButton {"
        "    background-color: %1;"
        "    border: none;"
        "    border-radius: 6px;"
        "    color: white;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 16px;"
        "    min-height: 20px;"
        "}"
        "QPushButton:hover {"
        "    background-color: %2;"
        "    transform: translateY(-1px);"
        "}"
        "QPushButton:pressed {"
        "    background-color: %3;"
        "    transform: translateY(0px);"
        "}"
        "QPushButton#minimizeButton, QPushButton#closeButton {"
        "    background-color: transparent;"
        "    border-radius: 15px;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    min-width: 30px;"
        "    max-width: 30px;"
        "    min-height: 30px;"
        "    max-height: 30px;"
        "    padding: 0px;"
        "}"
        "QPushButton#minimizeButton:hover {"
        "    background-color: %4;"
        "}"
        "QPushButton#closeButton:hover {"
        "    background-color: %5;"
        "}"
        "QPushButton#closeButton:pressed {"
        "    background-color: #c0392b;"
        "}"
        "QPushButton#testInfoButton {"
        "    background-color: %6;"
        "}"
        "QPushButton#testInfoButton:hover {"
        "    background-color: #005a9e;"
        "}"
        "QPushButton#testWarningButton {"
        "    background-color: %7;"
        "}"
        "QPushButton#testWarningButton:hover {"
        "    background-color: #e67e22;"
        "}"
        "QPushButton#testErrorButton {"
        "    background-color: %8;"
        "}"
        "QPushButton#testErrorButton:hover {"
        "    background-color: #c0392b;"
        "}"
        "QPushButton#clearLogButton {"
        "    background-color: #6c757d;"
        "}"
        "QPushButton#clearLogButton:hover {"
        "    background-color: #5a6268;"
        "}"
    ).arg(PRIMARY_COLOR, HOVER_COLOR, PRESSED_COLOR, HOVER_COLOR, ERROR_COLOR, 
         PRIMARY_COLOR, WARNING_COLOR, ERROR_COLOR);
}

QString ModernStyle::getLogTextEditStyle()
{
    return QString(
        "QTextEdit {"
        "    background-color: %1;"
        "    border: 1px solid %2;"
        "    border-radius: 6px;"
        "    color: %3;"
        "    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;"
        "    font-size: 12px;"
        "    padding: 10px;"
        "    selection-background-color: %4;"
        "    selection-color: white;"
        "}"
    ).arg(SURFACE_COLOR, BORDER_COLOR, TEXT_COLOR, PRIMARY_COLOR);
}

QString ModernStyle::getScrollBarStyle()
{
    return QString(
        "QScrollBar:vertical {"
        "    background-color: %1;"
        "    width: 12px;"
        "    border-radius: 6px;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:vertical {"
        "    background-color: #555555;"
        "    border-radius: 6px;"
        "    min-height: 20px;"
        "    margin: 2px;"
        "}"
        "QScrollBar::handle:vertical:hover {"
        "    background-color: #666666;"
        "}"
        "QScrollBar::handle:vertical:pressed {"
        "    background-color: %2;"
        "}"
        "QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {"
        "    border: none;"
        "    background: none;"
        "    height: 0px;"
        "}"
        "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {"
        "    background: none;"
        "}"
        "QScrollBar:horizontal {"
        "    background-color: %3;"
        "    height: 12px;"
        "    border-radius: 6px;"
        "    margin: 0px;"
        "}"
        "QScrollBar::handle:horizontal {"
        "    background-color: #555555;"
        "    border-radius: 6px;"
        "    min-width: 20px;"
        "    margin: 2px;"
        "}"
        "QScrollBar::handle:horizontal:hover {"
        "    background-color: #666666;"
        "}"
        "QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {"
        "    border: none;"
        "    background: none;"
        "    width: 0px;"
        "}"
    ).arg(SECONDARY_COLOR, PRIMARY_COLOR, SECONDARY_COLOR);
}

QString ModernStyle::getCompleteStyle()
{
    return getMainWindowStyle() + "\n" +
           getTitleBarStyle() + "\n" +
           getButtonStyle() + "\n" +
           getLogTextEditStyle() + "\n" +
           getScrollBarStyle() + "\n" +
           QString(
               "QWidget {"
               "    background-color: transparent;"
               "    color: %1;"
               "}"
               "QWidget#contentWidget {"
               "    background-color: %2;"
               "    border-bottom-left-radius: 7px;"
               "    border-bottom-right-radius: 7px;"
               "}"
           ).arg(TEXT_COLOR, BACKGROUND_COLOR);
}
