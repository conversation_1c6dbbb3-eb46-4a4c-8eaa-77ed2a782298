#pragma once

#include <QString>

/**
 * @brief 现代化样式管理类 - 2025年设计风格
 * 
 * 提供统一的现代化UI样式，包含深色主题、圆角设计、渐变效果等
 */
class ModernStyle
{
public:
    /**
     * @brief 获取主窗口样式
     * @return QSS样式字符串
     */
    static QString getMainWindowStyle();

    /**
     * @brief 获取标题栏样式
     * @return QSS样式字符串
     */
    static QString getTitleBarStyle();

    /**
     * @brief 获取按钮样式
     * @return QSS样式字符串
     */
    static QString getButtonStyle();

    /**
     * @brief 获取日志文本框样式
     * @return QSS样式字符串
     */
    static QString getLogTextEditStyle();

    /**
     * @brief 获取滚动条样式
     * @return QSS样式字符串
     */
    static QString getScrollBarStyle();

    /**
     * @brief 获取完整的应用程序样式
     * @return 完整的QSS样式字符串
     */
    static QString getCompleteStyle();

private:
    // 现代化配色方案
    static const QString PRIMARY_COLOR;        // 主色调
    static const QString SECONDARY_COLOR;      // 次要色调
    static const QString ACCENT_COLOR;         // 强调色
    static const QString BACKGROUND_COLOR;     // 背景色
    static const QString SURFACE_COLOR;        // 表面色
    static const QString TEXT_COLOR;           // 文本色
    static const QString TEXT_SECONDARY_COLOR; // 次要文本色
    static const QString BORDER_COLOR;         // 边框色
    static const QString HOVER_COLOR;          // 悬停色
    static const QString PRESSED_COLOR;        // 按下色
    static const QString SUCCESS_COLOR;        // 成功色
    static const QString WARNING_COLOR;        // 警告色
    static const QString ERROR_COLOR;          // 错误色
};
