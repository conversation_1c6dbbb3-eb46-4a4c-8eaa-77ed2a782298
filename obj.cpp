#include "pch.h"
#include "obj.h"
#include "MemoryModule.h"


static long g_dm_hmodule = 0;

typedef long (WINAPI* TypeMemoryLoad)(const void*, long, long);

// 解密
__declspec(noinline) UINT dmsystem::GameVmDecode(UINT value)
{
    UINT Myeax = 0;

    __asm
    {
        pushad
        sub esp, 0x50
        mov eax, value
        mov[esp + 0x30], eax
        movzx ecx, [esp + 0x32]
        movzx eax, cl

        shr al, 0x7
        xor cl, al

        movzx eax, cl
        shl al, 0x7
        xor cl, al

        movzx eax, cl
        shr al, 0x7
        xor cl, al
        xor cl, 0x63

        mov[esp + 0x32], cl
        mov eax, [esp + 0x30]
        add eax, 0xFA0A1357
        mov Myeax, eax
        add esp, 0x50
        popad

    }

    return Myeax;
}

// 加密
__declspec(noinline) UINT dmsystem::GameVmEncry(int value)
{
    UINT Myeax = 0;

    __asm
    {
        pushad
        sub esp, 0x50
        mov eax, value
        sub eax, 0xFA0A1357

        mov[esp + 0x30], eax
        movzx ecx, [esp + 0x32]
        movzx eax, cl

        xor cl, 0x63
        xor cl, al
        shl al, 0x7
        movzx eax, cl

        xor cl, al
        shr al, 0x7
        movzx eax, cl

        xor cl, al
        shl al, 0x7

        mov[esp + 0x32], cl
        mov eax, [esp + 0x30]
        mov Myeax, eax
        add esp, 0x50
        popad
    }

    return Myeax;

}



BOOL LoadDmMemory(const void* buffer, long size, const void* MemoryLoadData, long MemoryLoadSize)
{
    if (g_dm_hmodule) return TRUE;

    long hmodule_MemoryLoad = 0;
    TypeMemoryLoad MemoryLoad = NULL;
    hmodule_MemoryLoad = (long)(ULONG_PTR)MemoryLoadLibrary(MemoryLoadData, (void**)&MemoryLoad);
    if (hmodule_MemoryLoad == 0) return FALSE;

    //g_dm_hmodule = MemoryLoad(buffer, size, 97680);
    g_dm_hmodule = MemoryLoad(buffer, size, 95536);
    
    MemoryFreeLibrary((void*)(ULONG_PTR)hmodule_MemoryLoad);

    if (g_dm_hmodule) return TRUE;

    return FALSE;

}

dmsoft::dmsoft()
{
    typedef long (WINAPI* TypeCreateObj)(void);

    //TypeCreateObj CreateObj = (TypeCreateObj)(ULONG_PTR)(g_dm_hmodule + 97536);
    TypeCreateObj CreateObj = (TypeCreateObj)(ULONG_PTR)(g_dm_hmodule + 95360);
    obj = CreateObj();
}

dmsoft::~dmsoft()
{
    typedef long (WINAPI* TypeReleaseObj)(long);

    //TypeReleaseObj ReleaseObj = (TypeReleaseObj)(ULONG_PTR)(g_dm_hmodule + 97632);
    TypeReleaseObj ReleaseObj = (TypeReleaseObj)(ULONG_PTR)(g_dm_hmodule + 95472);
    ReleaseObj(obj);
}

long dmsoft::Reg(PCSTR code, PCSTR ver)
{
    typedef long (WINAPI* TypeReg)(long, PCSTR, PCSTR);

    //TypeReg fun = (TypeReg)(ULONG_PTR)(g_dm_hmodule + 118976);
    TypeReg fun = (TypeReg)(ULONG_PTR)(g_dm_hmodule + 108464);
    return fun(obj, code, ver);
}

long dmsoft::DmGuard(long enable, PCSTR type)
{
    typedef long (WINAPI* TypeDmGuard)(long, long, PCSTR);

    //TypeDmGuard fun = (TypeDmGuard)(ULONG_PTR)(g_dm_hmodule + 115392);
    TypeDmGuard fun = (TypeDmGuard)(ULONG_PTR)(g_dm_hmodule + 121088);
    return fun(obj, enable, type);
}

long dmsoft::SetParam64ToPointer()
{

    typedef long (WINAPI* TypeSetParam64ToPointer)(long);

    //TypeSetParam64ToPointer fun = (TypeSetParam64ToPointer)(ULONG_PTR)(g_dm_hmodule + 109248);
    TypeSetParam64ToPointer fun = (TypeSetParam64ToPointer)(ULONG_PTR)(g_dm_hmodule + 101168);
    return fun(obj);
}

long dmsoft::SetMemoryHwndAsProcessId(long en)
{
    typedef long (WINAPI* TypeSetMemoryHwndAsProcessId)(long, long);

    //TypeSetMemoryHwndAsProcessId fun = (TypeSetMemoryHwndAsProcessId)(ULONG_PTR)(g_dm_hmodule + 108768);
    TypeSetMemoryHwndAsProcessId fun = (TypeSetMemoryHwndAsProcessId)(ULONG_PTR)(g_dm_hmodule + 113968);
    return fun(obj, en);
}

long dmsoft::DmGuardExtract(PCSTR type, PCSTR path)
{
    typedef long (WINAPI* TypeDmGuardExtract)(long, PCSTR, PCSTR);

    //TypeDmGuardExtract fun = (TypeDmGuardExtract)(ULONG_PTR)(g_dm_hmodule + 99808);
    TypeDmGuardExtract fun = (TypeDmGuardExtract)(ULONG_PTR)(g_dm_hmodule + 113776);
    return fun(obj, type, path);
}

long dmsoft::DmGuardLoadCustom(PCSTR type, PCSTR path)
{
    typedef long (WINAPI* TypeDmGuardLoadCustom)(long, PCSTR, PCSTR);

    TypeDmGuardLoadCustom fun = (TypeDmGuardLoadCustom)(ULONG_PTR)(g_dm_hmodule + 119408);
    //TypeDmGuardLoadCustom fun = (TypeDmGuardLoadCustom)(ULONG_PTR)(g_dm_hmodule + 117008);
    return fun(obj, type, path);
}

long dmsoft::ReadDataAddrToBin(long hwnd, LONGLONG addr, long len)
{
    __try {
        typedef long (WINAPI* TypeReadDataAddrToBin)(long, long, LONGLONG, long);

        //TypeReadDataAddrToBin fun = (TypeReadDataAddrToBin)(ULONG_PTR)(g_dm_hmodule + 108816);
        TypeReadDataAddrToBin fun = (TypeReadDataAddrToBin)(ULONG_PTR)(g_dm_hmodule + 123120);
        return fun(obj, hwnd, addr, len);
    }
    __except (1) { return 0; }
}

LONGLONG dmsoft::VirtualAllocEx(long hwnd, LONGLONG addr, long size, long type)
{
    typedef LONGLONG(WINAPI* TypeVirtualAllocEx)(long, long, LONGLONG, long, long);

    TypeVirtualAllocEx fun = (TypeVirtualAllocEx)(ULONG_PTR)(g_dm_hmodule + 117712);
    //TypeVirtualAllocEx fun = (TypeVirtualAllocEx)(ULONG_PTR)(g_dm_hmodule + 109072);
    return fun(obj, hwnd, addr, size, type);
}

long dmsoft::WriteDataAddrFromBin(long hwnd, LONGLONG addr, long data, long len)
{
    typedef long (WINAPI* TypeWriteDataAddrFromBin)(long, long, LONGLONG, long, long);
    TypeWriteDataAddrFromBin fun = (TypeWriteDataAddrFromBin)(ULONG_PTR)(g_dm_hmodule + 119920);
    //TypeWriteDataAddrFromBin fun = (TypeWriteDataAddrFromBin)(ULONG_PTR)(g_dm_hmodule + 97856);
    return fun(obj, hwnd, addr, data, len);
}

std::string dmsoft::DmGuardParams(PCSTR cmd, PCSTR sub_cmd, PCSTR param)
{
    typedef PCSTR(WINAPI* TypeDmGuardParams)(long, PCSTR, PCSTR, PCSTR);
    TypeDmGuardParams fun = (TypeDmGuardParams)(ULONG_PTR)(g_dm_hmodule + 116048);
    //TypeDmGuardParams fun = (TypeDmGuardParams)(ULONG_PTR)(g_dm_hmodule + 111248);
    return fun(obj, cmd, sub_cmd, param);
}

LONGLONG dmsoft::GetModuleBaseAddr(long hwnd, PCSTR module_name)
{
    typedef LONGLONG(WINAPI* TypeGetModuleBaseAddr)(long, long, PCSTR);

    TypeGetModuleBaseAddr fun = (TypeGetModuleBaseAddr)(ULONG_PTR)(g_dm_hmodule + 120544);
    //TypeGetModuleBaseAddr fun = (TypeGetModuleBaseAddr)(ULONG_PTR)(g_dm_hmodule + 121264);
    return fun(obj, hwnd, module_name);
}

long dmsoft::VirtualProtectEx(long hwnd, LONGLONG addr, long size, long type, long old_protect)
{
    typedef long (WINAPI* TypeVirtualProtectEx)(long, long, LONGLONG, long, long, long);

    TypeVirtualProtectEx fun = (TypeVirtualProtectEx)(ULONG_PTR)(g_dm_hmodule + 108960);
    //TypeVirtualProtectEx fun = (TypeVirtualProtectEx)(ULONG_PTR)(g_dm_hmodule + 115024);
    return fun(obj, hwnd, addr, size, type, old_protect);
}

std::string dmsoft::VirtualQueryEx(long hwnd, LONGLONG addr, long pmbi)
{
    typedef PCSTR(WINAPI* TypeVirtualQueryEx)(long, long, LONGLONG, long);

    TypeVirtualQueryEx fun = (TypeVirtualQueryEx)(ULONG_PTR)(g_dm_hmodule + 110464);
    //TypeVirtualQueryEx fun = (TypeVirtualQueryEx)(ULONG_PTR)(g_dm_hmodule + 103280);
    return fun(obj, hwnd, addr, pmbi);
}

std::string dmsoft::ReadDataAddr(long hwnd, LONGLONG addr, long len)
{
    typedef PCSTR(WINAPI* TypeReadDataAddr)(long, long, LONGLONG, long);

    TypeReadDataAddr fun = (TypeReadDataAddr)(ULONG_PTR)(g_dm_hmodule + 104816);
    return fun(obj, hwnd, addr, len);
}

void dmsoft::Restore()
{
    KeyUp(37);
    KeyUp(38);
    KeyUp(39);
    KeyUp(40);
}

long dmsoft::KeyDown(long vk, int num)
{
    if (num)
    {
        KeyPress(vk);
        ::Sleep(100);
    }
    typedef long (WINAPI* TypeKeyDown)(long, long);

    TypeKeyDown fun = (TypeKeyDown)(ULONG_PTR)(g_dm_hmodule + 105504);
    return fun(obj, vk);
}

long dmsoft::KeyPress(long vk)
{
    typedef long (WINAPI* TypeKeyPress)(long, long);

    TypeKeyPress fun = (TypeKeyPress)(ULONG_PTR)(g_dm_hmodule + 106016);
    return fun(obj, vk);
}

long dmsoft::KeyUp(long vk)
{
    typedef long (WINAPI* TypeKeyUp)(long, long);

    TypeKeyUp fun = (TypeKeyUp)(ULONG_PTR)(g_dm_hmodule + 108592);
    return fun(obj, vk);
}

long dmsoft::GetKeyState(long vk)
{
    typedef long (WINAPI* TypeGetKeyState)(long, long);

    TypeGetKeyState fun = (TypeGetKeyState)(ULONG_PTR)(g_dm_hmodule + 102288);
    return fun(obj, vk);
}

long dmsoft::SetSimMode(long mode)
{
    typedef long (WINAPI* TypeSetSimMode)(long, long);

    //TypeSetSimMode fun = (TypeSetSimMode)(ULONG_PTR)(g_dm_hmodule + 116912);
    TypeSetSimMode fun = (TypeSetSimMode)(ULONG_PTR)(g_dm_hmodule + 97648);
    return fun(obj, mode);
}
long dmsoft::UnLoadDriver()
{
    typedef long (WINAPI* TypeUnLoadDriver)(long);

    TypeUnLoadDriver fun = (TypeUnLoadDriver)(ULONG_PTR)(g_dm_hmodule + 106096);
    return fun(obj);
}


std::string dmsoft::Ver()
{
    typedef PCSTR(WINAPI* TypeVer)(long);
    //TypeVer fun = (TypeVer)(ULONG_PTR)(g_dm_hmodule + 111776);
    TypeVer fun = (TypeVer)(ULONG_PTR)(g_dm_hmodule + 98032);
    return fun(obj);
}

dmsystem::dmsystem():m_proecssId(NULL)
{

}

dmsystem::~dmsystem()
{
    if (m_proecssId)
    {

    }
}





VOID dmsystem::initproess(DWORD dproecssid, bool Mode)
{
    if (m_proecssId != dproecssid)
    {
        m_proecssId = dproecssid;
        SetMemoryHwndAsProcessId(1);
        //auto aaa = DmGuard(1, xorstr_("memory2"));
        if (Mode) { SetSimMode(1); }
    }
}

void dmsystem::DmGuardKill()
{
    DmGuard(1, xorstr_("phide remove <0>"));
    DmGuard(1, xorstr_("h3 cls"));
    DmGuard(0, xorstr_("b2"));
    DmGuard(0, xorstr_("memory2"));
    UnLoadDriver();

}

_BYTES  dmsystem::Readdata(LONGLONG addr, long len)
{
    long lpData = 0;
    _BYTES  data;

    lpData = ReadDataAddrToBin(m_proecssId, addr, len);

    if (lpData == 0) { return data; }

    for (long i = 0; i < len; i++) 
    {
        data.push_back(*(PBYTE)(lpData + i));
    }

    return data;

}

__declspec(noinline) std::wstring dmsystem::ReadWstr(LONGLONG addr, long len)
{
 
    long lpData = 0;
    std::wstring result;
    wchar_t* neWchar = new wchar_t[len + 2];
    ZeroMemory(neWchar, len + 2);

    lpData = ReadDataAddrToBin(m_proecssId, addr, len);
    if (lpData == 0) { return result; }

    memcpy(neWchar, (void*)lpData, len);

    result = neWchar;
    delete[len + 2] neWchar;

    return result;

}

void HookCall::mnewMmemory(__int64 Gamehook, __int64 gameshellcode, __int64 value, __int64 error)
{
    
    m_pGamehook = Gamehook;
    m_pGameShellcode = gameshellcode;
    m_pValue = value;
    m_pError = error;

}

void HookCall::setthread(int threadid)
{

    m_pThreadId = threadid;

}

__int64 dmsystem::ReadDword(LONGLONG addr, long len)
{
 
    _BYTES  pcaddr = Readdata(addr, len);

    if (pcaddr.empty()) { return 0; }

    if ((int)pcaddr.size() < len) { return 0; }

    DWORD64 data = 0;

    memcpy(&data, &pcaddr[0], len);

    return data;
    
}


//初始化汇编信息 (暂时未开放)
void HookCall::inithook(int type)
{
    //1=动态Hook
    //2=VMhook
    //3=静态Hook
    m_int_hooktype = type;
}

HookCall::HookCall()
    :m_Pdm(nullptr),m_code(0)
{
    m_rsp = 0;
    
}

bool operator==(const std::vector<BYTE>& a, const std::vector<BYTE>& b)
{
    if (a.size() != b.size())return false;

    for (size_t t = 0; t < a.size(); t++) {
        if (a[t] != b[t])
            return false;
    }

    return true;
}

std::vector<BYTE>& operator+(std::vector<BYTE>& a, const std::vector<BYTE>& b)
{
    for (size_t i = 0; i < b.size(); i++)
        a.push_back(b[i]);

    return a;
}

_BYTES  getblankBYTE(size_t len)
{
    _BYTES  a;
    for (size_t i = 0; i < len; i++)
        a.push_back({0x0});
    return a;

}

std::vector<BYTE>& operator+=(std::vector<BYTE>& data1, const std::initializer_list<unsigned char>& data2)
{
    std::initializer_list<unsigned char>::const_iterator it = data2.begin();
    for (; it != data2.end(); it++)
        data1.push_back(*it);

    return data1;

}

std::vector<BYTE>& operator+=(std::vector<BYTE>& a, const std::vector<BYTE>& b)
{
    for (size_t i = 0; i < b.size(); i++)
        a.push_back(b[i]);
    return a;
}




void HookCall::installhook() {

    bool write = true;
    if (m_int_hooktype == 1)
    {
        __int64 cross_core = 0, intWindowProc = 0;
        __int64 addr = m_Pdm->ReadDword(OFSET_IAT_CLIPCURSOR);
        addr         = m_Pdm->ReadDword(m_Pdm->ReadDword(addr + 0x2, 4) + addr + 0x6);

        if (m_Pdm->Readdata(addr, 1) != _BYTES({ 0xE9 }))
        {
            addr     = m_Pdm->ReadDword(m_Pdm->ReadDword(addr + 0x2, 4) + addr + 0x6);
            int _add = (int)&addr;
            _add     = _add + 4;
            int _gs  = *(int*)_add;

            if (_gs)
            {
                UINT _ds = (int)&addr;
                _ds      = *(UINT*)_ds;
                _ds      = _ds + (int)m_Pdm->ReadDword(addr + 1, 4) + 5;
                memcpy(&addr, &_ds, 4);
                memcpy((int*)_add, &_gs, 4);
            }
        }
        else { addr = m_Pdm->ReadDword(addr + 1, 4) + addr + 5; }

        if (m_Pdm->Readdata(addr, 2) == _BYTES({ 0x80,0x3D })) { cross_core = addr - 0x54634; }
        if (!cross_core) { cross_core = m_Pdm->GetModuleBaseAddr(m_Pdm->m_proecssId, xorstr_("cross_core64.dll")); }
        int time = 0;
        do
        {
            time++;
            cross_core = m_Pdm->GetModuleBaseAddr(m_Pdm->m_proecssId, xorstr_("cross_core64.dll"));
            ::Sleep(100);

        } while (!cross_core && time < 100);


        intWindowProc   = cross_core + 0x5D906;
        m_i64newmemory  = m_Pdm->ReadDword(intWindowProc + 0x3, 4);
        m_i64newmemory  = intWindowProc + m_i64newmemory + 0x7;
        m_pRetAddr      = m_Pdm->ReadDword(m_i64newmemory);

        if (!m_pRetAddr) { m_int_hooktype = 2; }
       
    }    
    if (m_int_hooktype == 2)
    {
        __int64 tmp = m_Pdm->ReadDword(test_hook_TranslateMessage + 2, 4);
        tmp += test_hook_TranslateMessage + 2 + 4;
        tmp = m_Pdm->ReadDword(tmp);
        m_i64newmemory = m_Pdm->ReadDword(tmp + 2, 4);
        m_i64newmemory += tmp + 2 + 4;
        m_pRetAddr = m_Pdm->ReadDword(m_i64newmemory);
 
    }
    if (m_Pdm->Readdata(m_pRetAddr, 3) == _BYTES({ 0x54,0x50,0x53 }))
    {
        m_pRetAddr = m_Pdm->ReadDword(m_pRetAddr + 132);
        write = false;
    }
   
    if (write)
    {
        _BYTES backcode;
        backcode += { 0x54, 0x50, 0x53, 0x51, 0x52, 0x55, 0x56, 0x57, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41, 0x53, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x9C };
        backcode += { 0x65, 0x48, 0x8B, 0x04, 0x25, 0x30, 0x00, 0x00, 0x00, 0x8B, 0x40, 0x48 };

        backcode += { 0x3D };
        backcode += _toBYTE(m_pThreadId);
        backcode += { 0x75, 0x39, 0x48, 0xB8 };
        backcode += _toBYTE(m_pError);
        backcode += { 0x48, 0x83, 0x38, 0x01, 0x75, 0x29, 0x48, 0xB8 };
        backcode += _toBYTE(m_pGameShellcode);
        backcode += { 0xFF, 0xD0, 0x48, 0xBA };

        backcode += _toBYTE(m_pValue);
        backcode += { 0X48, 0X89, 0X02, 0x48, 0xB8 };
        backcode += _toBYTE(m_pError);

        backcode += { 0xC7, 0x00, 0x03, 0x00, 0x00, 0x00 };
        backcode += { 0x9D, 0x41, 0x5F, 0x41, 0x5E, 0x41, 0x5D, 0x41, 0x5C, 0x41, 0x5B, 0x41, 0x5A, 0x41, 0x59, 0x41, 0x58, 0x5F, 0x5E, 0x5D, 0x5A, 0x59, 0x5B, 0x58, 0x5C };

        backcode += { 255, 37 };
        backcode += _toBYTE(0);
        backcode += _toBYTE(m_pRetAddr);


        m_Pdm->WriteData(m_pGamehook, backcode);
        m_Pdm->WriteData(m_i64newmemory, m_pGamehook);
 
    }


}

__int64 HookCall::startcall(std::vector<BYTE> shell)
{
    m_lock.lock();
    size_t size = shell.size();
    if (size > 100000 && size <= 0) { return -1; }

    if ((int)m_Pdm->ReadDword(m_pError, 4))
    {
        m_stime = 0;
        while ((int)m_Pdm->ReadDword(m_pError, 4) && m_stime < 1000)
        {
            m_stime++;
            ::Sleep(1);
        }
        m_Pdm->WriteData(m_pError, 0);
    }

    if (m_bool_hooksleep)
    {
        m_stime = 0;
        while (m_bool_hooksleep  && m_stime != 1000)
        {
            ::Sleep(1);
            m_stime++;
        }
        m_bool_hooksleep = false;
    }
    m_bool_hooksleep = true;
    shell += {195};

    m_Pdm->WriteData(m_pGameShellcode, shell);
    m_Pdm->WriteData(m_pError, 1);
    m_stime = 0;

    while ((int)m_Pdm->ReadDword(m_pError, 4) != 3)
    {
        if (!(int)m_Pdm->ReadDword(m_pError, 4))
        {
            m_Pdm->WriteData(m_pError, 1);
            m_Pdm->WriteData(m_pGameShellcode, _BYTES({ 195 }));
            m_bool_hooksleep = false;
        }
        m_stime++;
        ::Sleep(1);
    }

    m_Pdm->WriteData(m_pError, 0);
    auto value = m_Pdm->ReadDword(m_pValue);
    m_Pdm->WriteData(m_pValue, (__int64)0);
    m_Pdm->WriteData(m_pGameShellcode, getblankBYTE(size + 2));

    m_bool_hooksleep = false;
    m_lock.unlock();
    return value;

}

void HookCall::addAddbytes(std::vector<BYTE> code)
{
    m_AddBytes += code;
}

void HookCall::add_rsp(const int rsp)
{
    _BYTES shellcode;
    m_rsp = rsp;

    shellcode += { 0X48,0x81,0Xc4 };
    shellcode += _toBYTE(rsp);
    shellcode.push_back(0xc3);

    m_AddBytes += shellcode;
}

void HookCall::sub_rsp(const int rsp)
{
    _BYTES shellcode;
    m_rsp = rsp;

    shellcode = { 0X48,0x81,0Xec };
    shellcode += _toBYTE(rsp);

    m_AddBytes += shellcode;
}

void HookCall::Set_rbp(const int rsp)
{
    m_rsp = rsp;
}


const std::vector<BYTE> HookCall::getAddBytes()
{
    return m_AddBytes;
}

__int64 HookCall::startcall(void)
{
    m_AddBytes += {195};
    auto rax = startcall(m_AddBytes);
    mdeletefast();
    return rax;
}

void HookCall::recoverhook()
{

}

void HookCall::Sethookthread()
{
}

int HookCall::gethookstate()
{
    if (!m_i64newmemory) { return HOOKCALL_STATE_ID_NOINIT; }
    return m_bool_hooksleep == false ? HOOKCALL_STATE_ID_SLEEP : HOOKCALL_STATE_ID_SERVICE;

}

void HookCall::mdeletefast()
{
    m_rsp = 0;
    std::vector<BYTE>().swap(m_AddBytes);
}

void HookCall::Set(dmsystem* dm)
{
    m_Pdm = dm;
}


