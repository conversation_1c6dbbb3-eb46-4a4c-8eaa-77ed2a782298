#include <iostream>
#include <QCoreApplication>
#include <QDebug>
#include "SkillDecisionEngine.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    std::cout << "开始技能决策系统测试..." << std::endl;
    
    try {
        // 创建技能决策引擎
        SkillDecisionEngine engine;
        
        // 创建测试数据
        QVector<MonsterInfo> monsters;
        
        // 添加一些测试怪物
        monsters.append(MonsterInfo(100, 200, "哥布林", 0));
        monsters.append(MonsterInfo(150, 180, "精英哥布林", 1));
        monsters.append(MonsterInfo(200, 220, "普通怪", 0));
        monsters.append(MonsterInfo(180, 200, "骷髅兵", 0));
        
        // 创建技能数据
        QVector<SkillInfo> skills;
        skills.append(SkillInfo("拔刀斩", "Q", true));
        skills.append(SkillInfo("普通攻击", "X", true));
        skills.append(SkillInfo("鬼斩", "W", true));
        skills.append(SkillInfo("上挑", "E", false)); // 冷却中
        
        // 角色位置
        QPointF playerPos(50, 50);
        
        std::cout << "测试数据准备完成" << std::endl;
        std::cout << "怪物数量: " << monsters.size() << std::endl;
        std::cout << "可用技能数量: " << skills.size() << std::endl;
        
        // 执行决策
        DecisionResult result = engine.makeDecision(monsters, skills, playerPos);
        
        // 输出结果
        std::cout << "\n=== 决策结果 ===" << std::endl;
        std::cout << "推荐技能: " << result.skillName.toStdString() 
                  << " (" << result.skillKey.toStdString() << ")" << std::endl;
        std::cout << "目标位置: (" << result.targetPos.x() 
                  << ", " << result.targetPos.y() << ")" << std::endl;
        std::cout << "预期命中: " << result.expectedHits << " 个目标" << std::endl;
        std::cout << "效率评分: " << result.efficiency << std::endl;
        std::cout << "策略说明: " << result.strategy.toStdString() << std::endl;
        std::cout << "决策原因: " << result.reason.toStdString() << std::endl;
        
        // 测试拔刀斩专门的AOE计算
        std::cout << "\n=== 拔刀斩AOE测试 ===" << std::endl;
        QPointF aoePos = engine.calculateOptimalAOEPosition(monsters, 150.0f, playerPos, 300.0f);
        int hits = engine.calculateExpectedHits(monsters, aoePos, 150.0f);
        std::cout << "最佳AOE位置: (" << aoePos.x() << ", " << aoePos.y() << ")" << std::endl;
        std::cout << "预期命中数量: " << hits << std::endl;
        
        std::cout << "\n✅ 测试完成！系统运行正常。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
